---
#
# This is the canonical configuration for the `README.md`
# Run `make readme` to rebuild the `README.md`
#

# Name of this project
name: terraform-aws-security-group

# Tags of this project
tags:
  - aws
  - security-group
  - terraform
  - terraform-modules

# Logo for this project
#logo: docs/logo.png

# License of this project
license: "APACHE2"

# Copyrights
copyrights:
  - name: "Cloud Posse, LLC"
    url: "https://cloudposse.com"
    year: "2021"

# Canonical GitHub repo
github_repo: cloudposse/terraform-aws-security-group

# Badges to display
badges:
  - name: "Latest Release"
    image: "https://img.shields.io/github/release/cloudposse/terraform-aws-security-group.svg"
    url: "https://github.com/cloudposse/terraform-aws-security-group"
  - name: "Slack Community"
    image: "https://slack.cloudposse.com/badge.svg"
    url: "https://slack.cloudposse.com"

# List any related terraform modules that this module may be used with or that this module depends on.
related:
  - name: "terraform-null-label"
    description: "Terraform module designed to generate consistent names and tags for resources. Use terraform-null-label to implement a strict naming convention."
    url: "https://github.com/cloudposse/terraform-null-label"

# List any resources helpful for someone to get started. For example, link to the hashicorp documentation or AWS documentation.
references:
  - name: terraform-provider-aws
    description: Terraform AWS provider
    url: https://registry.terraform.io/providers/hashicorp/aws/latest

# Short description of this project
description: |-
  Terraform module to create AWS Security Group and rules.

# Introduction to the project
#introduction: |-
#  This is an introduction.

# How to use this module. Should be an easy example to copy and paste.
usage: |-
  This module is primarily for setting security group rules on a security group. You can provide the
  ID of an existing security group to modify, or, by default, this module will create a new security
  group and apply the given rules to it.

  ##### `rules` and `rules_map` inputs
  This module provides 3 ways to set security group rules. You can use any or all of them at the same time.

  The easy way to specify rules is via the `rules` input. It takes a list of rules. (We will define
  a rule [a bit later](#definition-of-a-rule).) The problem is that a Terraform list must be composed
  of elements that are all the exact same type, and rules can be any of several
  different Terraform types. So to get around this restriction, the second
  way to specify rules is via the `rules_map` input, which is more complex.

  <details><summary>Why the input is so complex (click to reveal)</summary>

  - Terraform has 3 basic simple types: bool, number, string
  - Terraform then has 3 collections of simple types: list, map, and set
  - Terraform then has 2 structural types: object and tuple. However, these are not really single
  types. They are catch-all labels for values that are themselves combination of other values.
  (This will become a bit clearer after we define `maps` and contrast them with `objects`)

  One [rule of the collection types](https://www.terraform.io/docs/language/expressions/type-constraints.html#collection-types)
  is that the values in the collections must all be the exact same type.
  For example, you cannot have a list where some values are boolean and some are string. Maps require
  that all keys be strings, but the map values can be any type, except again all the values in a map
  must be the same type. In other words, the values of a map must form a valid list.

  Objects look just like maps. The difference between an object and a map is that the values in an
  object do not all have to be the same type.

  The "type" of an object is itself an object: the keys are the same, and the values are the types of the values in the object.

  So although `{ foo = "bar", baz = {} }` and `{ foo = "bar", baz = [] }` are both objects,
  they are not of the same type, and you can get error messages like

  ```
  Error: Inconsistent conditional result types
  The true and false result expressions must have consistent types. The given
  expressions are object and object, respectively.
  ```

  This means you cannot put them both in the same list or the same map,
  even though you can put them in a single tuple or object.
  Similarly, and closer to the problem at hand,

  ```hcl
  cidr_rule = {
    type        = "ingress"
    cidr_blocks = ["0.0.0.0/0"]
  }
  ```
  is not the same type as

  ```hcl
  self_rule = {
    type        = "ingress"
    self        = true
  }
  ```

  This means you cannot put both of those in the same list.

  ```hcl
  rules = tolist([local.cidr_rule, local.self_rule])
  ```

  Generates the error

  ```text
  Invalid value for "v" parameter: cannot convert tuple to list of any single type.
  ```

  You could make them the same type and put them in a list,
  like this:

  ```hcl
  rules = tolist([{
    type        = "ingress"
    cidr_blocks = ["0.0.0.0/0"]
    self        = null
  },
  {
    type        = "ingress"
    cidr_blocks = []
    self        = true
  }])
  ```

  That remains an option for you when generating the rules, and is probably better when you have full control over all the rules.
  However, what if some of the rules are coming from a source outside of your control? You cannot simply add those rules
  to your list. So, what to do? Create an object whose attributes' values can be of different types.

  ```hcl
  { mine = local.my_rules, theirs = var.their_rules }
  ```

  That is why the `rules_map` input is available. It will accept a structure like that, an object whose
  attribute values are lists of rules, where the lists themselves can be different types.

  </details>

  The `rules_map` input takes an object.
  - The attribute names (keys) of the object can be anything you want, but need to be known during `terraform plan`,
  which means they cannot depend on any resources created or changed by Terraform.
  - The values of the attributes are lists of rule objects, each object representing one Security Group Rule. As explained
    above in "Why the input is so complex", each object in the list must be exactly the same type. To use multiple types,
    you must put them in separate lists which are values of separate attributes.

  ###### Definition of a rule

  For this module, a rule is defined as an object.
  - The attributes and values of the rule objects are fully compatible (have the same keys and accept the same values) as the
  Terraform [aws_security_group_rule resource](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule),
  except
     - The `security_group_id` will be ignored, if present
     - You can include an optional `key` attribute. If present, its value must be unique among all security group rules in the
       security group, and it must be known in the Terraform "plan" phase, meaning it cannot depend on anything being
       generated or created by Terraform.

  The `key` attribute value, if provided, will be used to identify the Security Group Rule to Terraform in order to
  prevent Terraform from modifying it unnecessarily. If the `key` is not provided, Terraform will assign an identifier
  based on the rule's position in its list, which can cause a ripple effect of rules being deleted and recreated if
  a rule gets deleted from start of a list, causing all the other rules to shift position.
  See ["Unexpected changes..."](#unexpected-changes-during-plan-and-apply) below for more details.


  ##### `rule_matrix` input
  The other way to set rules is via the `rule_matrix` input. This splits the attributes of the `aws_security_group_rule`
  resource into two sets: one set defines the rule and description, the other set defines the subjects of the rule.
  Again, optional "key" values can provide stability, but cannot contain derived values.

  As with `rules` and explained above in "Why the input is so complex", all elements of the list must be the exact same type.
  This also holds for all the elements of the `rules_matrix.rules` list. Because `rule_matrix` is already
  so complex, we do not provide the ability to mix types by packing object within more objects.
  All of the elements of the `rule_matrix` list must be exactly the same type. You can make them all the same
  type by following a few rules:

  - Every object in a list must have the exact same set of attributes. Most attributes are optional and can be omitted,
    but any attribute appearing in one object must appear in all the objects.
  - Any attribute that takes a list value in any object must contain a list in all objects.
    Use an empty list rather than `null` to indicate "no value". Passing in `null` instead of a list
    may cause Terraform to crash or emit confusing error messages (e.g. "number is required").
  - Any attribute that takes a value of type other than list can be set to `null` in objects where no value is needed.

  The schema for `rule_matrix` is:

  ```hcl
  {
    # these top level lists define all the subjects to which rule_matrix rules will be applied
    key                       = an optional unique key to keep these rules from being affected when other rules change
    source_security_group_ids = list of source security group IDs to apply all rules to
    cidr_blocks               = list of ipv4 CIDR blocks to apply all rules to
    ipv6_cidr_blocks          = list of ipv6 CIDR blocks to apply all rules to
    prefix_list_ids           = list of prefix list IDs to apply all rules to

    self = boolean value; set it to "true" to apply the rules to the created or existing security group, null otherwise

    # each rule in the rules list will be applied to every subject defined above
    rules = [{
      key       = an optional unique key to keep this rule from being affected when other rules change
      type      = type of rule, either "ingress" or "egress"
      from_port = start range of protocol port
      to_port   = end range of protocol port, max is 65535
      protocol  = IP protocol name or number, or "-1" for all protocols and ports

      description = free form text description of the rule
    }]
  }
  ```

  ##### Create before delete
  This module provides a `create_before_delete` option that will, when a security group needs to be replaced,
  cause Terraform to create the new one before deleting the old one. We recommend making this `true` for new security groups,
  but we default it to `false` because if you import a security group with this setting `true`, that security
  group will be deleted and replaced on the first `terraform apply`, which will likely cause a service outage.

  ### Important Notes

  ##### Unexpected changes during plan and apply
  The way Terraform works and the way this module is implemented causes security group rules without keys
  to be dependent on their place in the input lists. If a rule is deleted and the other rules therefore move
  closer to the start of the list, those rules will be deleted and recreated. This should have no significant
  operational impact, but it can make a small change look like a big one when viewing the output of
  Terraform plan.

  You can avoid this for the most part by providing the optional keys. Rules with keys will not be
  changed if their keys do not change and the rules themselves do not change, except in the case of
  `rule_matrix`, where the rules are still dependent on the order of the security groups in
  `source_security_group_ids`. You can avoid this by using `rules` instead of `rule_matrix` when you have
  more than one security group in the list. You cannot avoid this by sorting the
  `source_security_group_ids`, because that leads to the "Invalid `for_each` argument" error.

  ##### Invalid for_each argument
  You can supply a number of rules as inputs to this module, and they (usually) get transformed into
  `aws_security_group_rule` resources. However, Terraform works in 2 steps: a `plan` step where it
  calculates the changes to be made, and an `apply` step where it makes the changes. This is so you
  can review and approve the plan before changing anything. One big limitation of this approach is
  that it requires that Terraform be able to count the number of resources to create without the
  benefit of any data generated during the `apply` phase. So if you try to generate a rule based
  on something you are creating at the same time, you can get an error like

  ```
  Error: Invalid for_each argument
  The "for_each" value depends on resource attributes that cannot be determined until apply,
  so Terraform cannot predict how many instances will be created.
  ```

  This module uses lists to minimize the chance of that happening, as all it needs to know
  is the length of the list, not the values in it, but this error still can
  happen for subtle reasons. Most commonly, using a function like `compact` on a list
  will cause the length to become unknown (since the values have to be checked and `null`s removed).
  In the case of `source_security_group_ids`, just sorting the list using `sort`
  will cause this error. If you run into this error, check for functions like `compact` somewhere
  in the chain that produces the list and remove them if you find them.


  ##### WARNINGS and Caveats

  **_Setting `inline_rules_enabled` is not recommended and NOT SUPPORTED_**: Any issues arising from setting
  `inlne_rules_enabled = true` (including issues about setting it to `false` after setting it to `true`) will
  not be addressed, because they flow from [fundamental problems](https://github.com/hashicorp/terraform-provider-aws/issues/20046)
  with the underlying `aws_security_group` resource. The setting is provided for people who know and accept the
  limitations and trade-offs and want to use it anyway. The main advantage is that when using inline rules,
  Terraform will perform "drift detection" and attempt to remove any rules it finds in place but not
  specified inline. See [this post](https://github.com/hashicorp/terraform-provider-aws/pull/9032#issuecomment-639545250)
  for a discussion of the difference between inline and resource rules,
  and some of the reasons inline rules are not satisfactory.

  **_KNOWN ISSUE_** ([#20046](https://github.com/hashicorp/terraform-provider-aws/issues/20046)):
  If you set `inline_rules_enabled = true`, you cannot later set it to `false`. If you try,
  Terraform will [complain](https://github.com/hashicorp/terraform/pull/2376) and fail.
  You will either have to delete and recreate the security group or manually delete all
  the security group rules via the AWS console or CLI before applying `inline_rules_enabled = false`.

  **_Objects not of the same type_**: Any time you provide a list of objects, Terraform requires that all objects in the list
  must be [the exact same type](https://www.terraform.io/docs/language/expressions/type-constraints.html#dynamic-types-the-quot-any-quot-constraint).
  This means that all objects in the list have exactly the same set of attributes and that each attribute has the same type
  of value in every object. So while some attributes are optional for this module, if you include an attribute in any one of the objects in a list, then you
  have to include that same attribute in all of them.  In rules where the key would othewise be omitted, include the key with value of `null`,
  unless the value is a list type, in which case set the value to `[]` (an empty list), due to [#28137](https://github.com/hashicorp/terraform/issues/28137).


# Example usage
examples: |-

  See [examples/complete/main.tf](https://github.com/cloudposse/terraform-aws-security-group/examples/complete/main.tf) for
  even more examples.

  ```hcl
  module "label" {
    source = "cloudposse/label/null"
    # Cloud Posse recommends pinning every module to a specific version
    # version = "x.x.x"
    namespace  = "eg"
    stage      = "prod"
    name       = "bastion"
    attributes = ["public"]
    delimiter  = "-"

    tags = {
      "BusinessUnit" = "XYZ",
      "Snapshot"     = "true"
    }
  }

  module "vpc" {
    source = "cloudposse/vpc/aws"
    # Cloud Posse recommends pinning every module to a specific version
    # version = "x.x.x"
    cidr_block = "10.0.0.0/16"

    context = module.label.context
  }

  module "sg" {
    source = "cloudposse/security-group/aws"
    # Cloud Posse recommends pinning every module to a specific version
    # version = "x.x.x"

    # Security Group names must be unique within a VPC.
    # This module follows Cloud Posse naming conventions and generates the name
    # based on the inputs to the null-label module, which means you cannot
    # reuse the label as-is for more than one security group in the VPC.
    #
    # Here we add an attribute to give the security group a unique name.
    attributes = ["primary"]

    # Allow unlimited egress
    allow_all_egress = true

    rules = [
      {
        key         = "ssh"
        type        = "ingress"
        from_port   = 22
        to_port     = 22
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
        self        = null
        description = "Allow SSH from anywhere"
      },
      {
        key         = "HTTP"
        type        = "ingress"
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = []
        self        = true
        description = "Allow HTTP from inside the security group"
      }
    ]

    vpc_id  = module.vpc.vpc_id

    context = module.label.context
  }

  module "sg_mysql" {
    source = "cloudposse/security-group/aws"
    # Cloud Posse recommends pinning every module to a specific version
    # version = "x.x.x"

    # Add an attribute to give the Security Group a unique name
    attributes = ["mysql"]

    # Allow unlimited egress
    allow_all_egress = true

    rule_matrix =[
      # Allow any of these security groups or the specified prefixes to access MySQL
      {
        source_security_group_ids = [var.dev_sg, var.uat_sg, var.staging_sg]
        prefix_list_ids = [var.mysql_client_prefix_list_id]
        rules = [
          {
            key         = "mysql"
            type        = "ingress"
            from_port   = 3306
            to_port     = 3306
            protocol    = "tcp"
            description = "Allow MySQL access from trusted security groups"
          }
        ]
      }
    ]

    vpc_id  = module.vpc.vpc_id

    context = module.label.context
  }

  ```


# How to get started quickly
#quickstart: |-
#  Here's how to get started...

# Other files to include in this README from the project folder
include:
  - "docs/targets.md"
  - "docs/terraform.md"

# Contributors to this project
contributors:
  - name: "Erik Osterman"
    github: "osterman"
  - name: "Nuru"
    github: "Nuru"
  - name: "Vladimir"
    github: "SweetOps"
  - name: "RB"
    github: "nitrocode"
  - name: "Andriy Knysh"
    github: "aknysh"
