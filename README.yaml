#
# This is the canonical configuration for the `README.md`
# Run `make readme` to rebuild the `README.md`
#

# Name of this project
name: terraform-aws-route53-cluster-hostname
# Tags of this project
tags:
  - aws
  - terraform
  - terraform-modules
  - networking
  - route53
  - cluster
  - hostname
# Categories of this project
categories:
  - terraform-modules/networking
# Logo for this project
#logo: docs/logo.png

# License of this project
license: "APACHE2"
# Canonical GitHub repo
github_repo: cloudposse/terraform-aws-route53-cluster-hostname
# Badges to display
badges:
  - name: "Latest Release"
    image: "https://img.shields.io/github/release/cloudposse/terraform-aws-route53-cluster-hostname.svg"
    url: "https://github.com/cloudposse/terraform-aws-route53-cluster-hostname/releases/latest"
  - name: "Slack Community"
    image: "https://slack.cloudposse.com/badge.svg"
    url: "https://slack.cloudposse.com"
related:
  - name: "terraform-aws-route53-alias"
    description: "Terraform module to define vanity host/domain (e.g. `brand.com`) as an ALIAS record"
    url: "https://github.com/cloudposse/terraform-aws-route53-alias"
  - name: "terraform-aws-route53-cluster-zone"
    description: "Terraform module to provision cluster domain (e.g. `prod.ourcompany.com`)"
    url: "https://github.com/cloudposse/terraform-aws-route53-cluster-zone"
  - name: "terraform-aws-kops-route53"
    description: "Terraform module to lookup the IAM role associated with `kops` masters, and attach an IAM policy to the role with permissions to modify Route53 record sets"
    url: "https://github.com/cloudposse/terraform-aws-kops-route53"
# Short description of this project
description: |-
  Terraform module to define a consistent AWS Route53 hostname
include:
  - "docs/targets.md"
  - "docs/terraform.md"
# Contributors to this project
contributors:
  - name: "Erik Osterman"
    github: "osterman"
  - name: "Igor Rodionov"
    github: "goruha"
  - name: "Andriy Knysh"
    github: "aknysh"
  - name: "Lucas Pearson"
    github: "pearson-lucas-dev"
  - name: "Daren Desjardins"
    github: "darend"
