---
name: Feature Request
description: Suggest an idea for this project
labels: ["feature request"]
assignees: [""]
body:
  - type: markdown
    attributes:
      value: |
        Have a question?
        
        Please checkout our [Slack Community](https://slack.cloudposse.com)
        or visit our [Slack Archive](https://archive.sweetops.com/).

        [![Slack Community](https://slack.cloudposse.com/badge.svg)](https://slack.cloudposse.com)

  - type: textarea
    id: concise-description
    attributes:
      label: Describe the Feature
      description: A clear and concise description of what the feature is.
      placeholder: What is the feature about?
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected.
      placeholder: What happened?
    validations:
      required: true

  - type: textarea
    id: use-case
    attributes:
      label: Use Case
      description: |
        Is your feature request related to a problem/challenge you are trying
        to solve?
        
        Please provide some additional context of why this feature or
        capability will be valuable.
    validations:
      required: true

  - type: textarea
    id: ideal-solution
    attributes:
      label: Describe Ideal Solution
      description: A clear and concise description of what you want to happen.
    validations:
      required: true

  - type: textarea
    id: alternatives-considered
    attributes:
      label: Alternatives Considered
      description: Explain alternative solutions or features considered.
    validations:
      required: false

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: |
        Add any other context about the problem here.
    validations:
      required: false
