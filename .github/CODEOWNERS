# Use this file to define individuals or teams that are responsible for code in a repository.
# Read more: <https://help.github.com/articles/about-codeowners/>
#
# Order is important: the last matching pattern has the highest precedence

# These owners will be the default owners for everything
*             @cloudposse/engineering @cloudposse/contributors

# Cloud Posse must review any changes to Makefiles
**/Makefile   @cloudposse/engineering
**/Makefile.* @cloudposse/engineering

# Cloud Posse must review any changes to GitHub actions
.github/*     @cloudposse/engineering

# Cloud Posse must review any changes to standard context definition,
# but some changes can be rubber-stamped.
**/*.tf       @cloudposse/engineering @cloudposse/contributors @cloudposse/approvers
README.yaml   @cloudposse/engineering @cloudposse/contributors @cloudposse/approvers
README.md     @cloudposse/engineering @cloudposse/contributors @cloudposse/approvers
docs/*.md     @cloudposse/engineering @cloudposse/contributors @cloudposse/approvers

# Cloud Posse Admins must review all changes to CODEOWNERS or the mergify configuration
.github/mergify.yml @cloudposse/admins
.github/CODEOWNERS  @cloudposse/admins
