name: auto-release

on:
  push:
    branches:
      - main
      - master
      - production

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      # Get PR from merged commit to master
      - uses: actions-ecosystem/action-get-merged-pull-request@v1
        id: get-merged-pull-request
        with:
          github_token: ${{ secrets.PUBLIC_REPO_ACCESS_TOKEN }}
      # Drafts your next Release notes as Pull Requests are merged into "main"
      - uses: release-drafter/release-drafter@v5
        with:
          publish: ${{ !contains(steps.get-merged-pull-request.outputs.labels, 'no-release') }}
          prerelease: false
          config-name: auto-release.yml
        env:
          GITHUB_TOKEN: ${{ secrets.PUBLIC_REPO_ACCESS_TOKEN }}
