name: Validate Codeowners
on:
  workflow_dispatch:

  pull_request:

jobs:
  validate-codeowners:
    runs-on: ubuntu-latest
    steps:
    - name: "Checkout source code at current commit"
      uses: actions/checkout@v2
    - uses: mszostok/codeowners-validator@v0.5.0
      if: github.event.pull_request.head.repo.full_name == github.repository
      name: "Full check of CODEOWNERS"
      with:
        # For now, remove "files" check to allow CODEOWNERS to specify non-existent
        # files so we can use the same CODEOWNERS file for Terraform and non-Terraform repos
        #   checks: "files,syntax,owners,duppatterns"
        checks: "syntax,owners,duppatterns"
        # GitHub access token is required only if the `owners` check is enabled
        github_access_token: "${{ secrets.PUBLIC_REPO_ACCESS_TOKEN }}"
    - uses: mszostok/codeowners-validator@v0.5.0
      if: github.event.pull_request.head.repo.full_name != github.repository
      name: "Syntax check of CODEOWNERS"
      with:
        checks: "syntax,duppatterns"
