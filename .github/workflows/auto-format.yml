name: Auto Format
on:
  pull_request_target:
    types: [opened, synchronize]

jobs:
  auto-format:
    runs-on: ubuntu-latest
    container: cloudposse/build-harness:latest
    steps:
    # Checkout the pull request branch
    #  "An action in a workflow run can’t trigger a new workflow run. For example, if an action pushes code using
    #   the repository’s GITHUB_TOKEN, a new workflow will not run even when the repository contains
    #   a workflow configured to run when push events occur."
    # However, using a personal access token will cause events to be triggered.
    # We need that to ensure a status gets posted after the auto-format commit.
    # We also want to trigger tests if the auto-format made no changes.
    - uses: actions/checkout@v2
      if: github.event.pull_request.state == 'open'
      name: Privileged Checkout
      with:
        token: ${{ secrets.REPO_ACCESS_TOKEN }}
        repository: ${{ github.event.pull_request.head.repo.full_name }}
        # Check out the PR commit, not the merge commit
        # Use `ref` instead of `sha` to enable pushing back to `ref`
        ref: ${{ github.event.pull_request.head.ref }}

    # Do all the formatting stuff
    - name: Auto Format
      if: github.event.pull_request.state == 'open'
      shell: bash
      env:
        GITHUB_TOKEN: "${{ secrets.REPO_ACCESS_TOKEN }}"
      run: make BUILD_HARNESS_PATH=/build-harness PACKAGES_PREFER_HOST=true -f /build-harness/templates/Makefile.build-harness pr/auto-format/host

    # Commit changes (if any) to the PR branch
    - name: Commit changes to the PR branch
      if: github.event.pull_request.state == 'open'
      shell: bash
      id: commit
      env:
        SENDER: ${{ github.event.sender.login }}
      run: |
        set -x
        output=$(git diff --name-only)

        if [ -n "$output" ]; then
          echo "Changes detected. Pushing to the PR branch"
          git config --global user.name 'cloudpossebot'
          git config --global user.email '<EMAIL>'
          git add -A
          git commit -m "Auto Format"
          # Prevent looping by not pushing changes in response to changes from cloudpossebot
          [[ $SENDER ==  "cloudpossebot" ]] || git push
          # Set status to fail, because the push should trigger another status check,
          # and we use success to indicate the checks are finished.
          echo "changed=true" >> "$GITHUB_OUTPUT"
          exit 1
        else
          echo "changed=false" >> "$GITHUB_OUTPUT"
          echo "No changes detected"
        fi

    - name: Auto Test
      uses: cloudposse/actions/github/repository-dispatch@0.30.0
      # match users by ID because logins (user names) are inconsistent,
      # for example in the REST API Renovate Bot is `renovate[bot]` but
      # in GraphQL it is just `renovate`, plus there is a non-bot
      # user `renovate` with ID 1832810.
      # Mergify bot: 37929162
      # Renovate bot: 29139614
      # Cloudpossebot: 11232728
      # Need to use space separators to prevent "21" from matching "112144"
      if: >
        contains(' 37929162 29139614 11232728 ', format(' {0} ', github.event.pull_request.user.id))
        && steps.commit.outputs.changed == 'false' && github.event.pull_request.state == 'open'
      with:
        token: ${{ secrets.REPO_ACCESS_TOKEN }}
        repository: cloudposse/actions
        event-type: test-command
        client-payload: |-
          { "slash_command":{"args": {"unnamed": {"all": "all", "arg1": "all"}}},
             "pull_request": ${{ toJSON(github.event.pull_request) }},
             "github":{"payload":{"repository": ${{ toJSON(github.event.repository) }},
                                  "comment": {"id": ""}
                                 }
                      }
          }
