name: chatops
on:
  issue_comment:
    types: [created]

jobs:
  default:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: "Handle common commands"
        uses: cloudposse/actions/github/slash-command-dispatch@0.22.0
        with:
          token: ${{ secrets.PUBLIC_REPO_ACCESS_TOKEN }}
          reaction-token: ${{ secrets.GITHUB_TOKEN }}
          repository: cloudposse/actions
          commands: rebuild-readme, terraform-fmt
          permission: triage
          issue-type: pull-request

  test:
    runs-on: ubuntu-latest
    steps:
      - name: "Checkout commit"
        uses: actions/checkout@v2
      - name: "Run tests"
        uses: cloudposse/actions/github/slash-command-dispatch@0.22.0
        with:
          token: ${{ secrets.PUBLIC_REPO_ACCESS_TOKEN }}
          reaction-token: ${{ secrets.GITHUB_TOKEN }}
          repository: cloudposse/actions
          commands: test
          permission: triage
          issue-type: pull-request
          reactions: false


