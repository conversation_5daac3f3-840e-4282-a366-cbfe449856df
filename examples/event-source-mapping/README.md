# Event Source Mapping configuration

Configuration in this directory creates Lambda Function with event source mapping configuration for SQS queue, Kinesis stream, Amazon MQ, and DynamoDB table.

## Usage

To run this example you need to execute:

```bash
$ terraform init
$ terraform plan
$ terraform apply
```

Note that this example may create resources which cost money. Run `terraform destroy` when you don't need these resources.
