resource "aws_ecr_repository" "tol_lambda_eks" {
  name = "tol-lambda-eks-${var.environment}"
}

resource "aws_ecr_repository_policy" "tol_lambda_eks_policy" {
  repository = aws_ecr_repository.tol_lambda_eks.name
  policy     = <<EOF
{
  "Version": "2008-10-17",
  "Statement": [
    {
      "Sid": "LambdaECRPermissions",
      "Effect": "Allow",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Action": [
        "ecr:BatchGetImage",
        "ecr:DeleteRepositoryPolicy",
        "ecr:GetDownloadUrlForLayer",
        "ecr:GetRepositoryPolicy",
        "ecr:SetRepositoryPolicy"
      ],
      "Condition": {
        "StringLike": {
          "aws:sourceArn": "arn:aws:lambda:ca-central-1:${var.account_id}:function:*"
        }
      }
    }
  ]
}
EOF
}


data "aws_ecr_authorization_token" "token" {}

module "lambda_function" {
  source  = "terraform-aws-modules/lambda/aws"
  version = "~> 3.0"

  function_name          = var.name
  create_package         = false
  description            = "Lambda function using ECR image for EKS integration"
  ephemeral_storage_size = 512
  memory_size            = 512
  timeout                = 300
  package_type           = "Image"
  image_uri              = "${aws_ecr_repository.tol_lambda_eks.repository_url}:${var.image_id}"
  publish                = true

  environment_variables = {
    CLUSTER_NAME  = var.eks_name
    JOB_NAME      = "etransfer-recon-job"
    LAMBDA_REGION = var.region
    NAMESPACE     = "pg-ledger-${var.environment}"
  }

  allowed_triggers = {
    s3_object_created_input = {
      principal      = "s3.amazonaws.com"
      source_arn     = var.s3_bucket_notification_arn
      source_account = "************"
      event          = "s3:ObjectCreated:*"
      filter_prefix  = "input"
    }
  }

  tags = var.tags
}
