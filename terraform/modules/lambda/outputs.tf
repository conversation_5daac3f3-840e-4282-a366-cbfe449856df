output "lambda_function_arn" {
  value = module.lambda_function.lambda_function_arn
}

output "ecr_image_uri" {
  description = "The ECR image URI for the Lambda function"
  value       = "${aws_ecr_repository.tol_lambda_eks.repository_url}:latest"
}

output "lambda_role_arn" {
  description = "The ARN of the Lambda execution role"
  value       = module.lambda_function.lambda_role_arn
}

output "lambda_role_name" {
  description = "The name of the Lambda execution role"
  value       = module.lambda_function.lambda_role_name
}