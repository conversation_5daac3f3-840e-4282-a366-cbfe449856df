resource "aws_iam_policy" "lambda_eks_policy" {
  name        = "lambda-eks-policy-${var.environment}"
  description = "Allow Lambda to list and describe EKS clusters and access S3"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "eks:ListClusters",
          "eks:DescribeCluster"
        ]
        Resource = "arn:aws:eks:${var.region}:${var.account_id}:cluster/${var.eks_name}"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = [
          var.s3_bucket_notification_arn,
          "${var.s3_bucket_notification_arn}/*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_eks_policy_attachment" {
  role       = module.lambda_function.lambda_role_name
  policy_arn = aws_iam_policy.lambda_eks_policy.arn
}
