variable "environment" {
  description = "The environment for the Lambda function (e.g., dev, prod)"
  type        = string
}

variable "name" {
  description = "Name of the Lambda function"
  type        = string
}

variable "tags" {
  description = "Tags to apply to the Lambda function"
  type        = map(any)
}

variable "s3_bucket_notification_arn" {
  description = "The ARN of the S3 bucket notification for Lambda trigger"
  type        = string
}

variable "region" {
  description = "AWS region where the Lambda function is deployed"
  type        = string
}

variable "account_id" {
  description = "AWS account ID where the Lambda function is deployed"
  type        = string
}

variable "eks_name" {
  description = "Name of the EKS cluster to which this Lambda function is associated"
  type        = string
}

variable "image_id" {
  description = "The ID of the Docker image to use for the Lambda function"
  type        = string
}