variable "enable_flow_logs" {
  description = "Set to false destroy all associated resources of this module"
}

variable "environment" {
  description = "Environment that this VPC corresponds to"
}

variable "flow_logs_iam_role_arn" {
  description = "IAM Role assumed by vpc-flow-logs allowing it CRUD operation over cloudwatch log groups/streams"
}

variable "project" {
  description = "Project where this module is ised"
}

variable "traffic_type" {
  description = "ACCEPT, REJECT or ALL"
}

variable "vpc_id" {
  description = "AWS VPC Id"
}
