# resource "aws_elasticache_subnet_group" "this" {
#   name       = "${var.project}-elastic-cache-subnet-group-${var.environment}"
#   subnet_ids = var.subnet_ids
# }

resource "aws_elasticache_subnet_group" "this" {
  name       = "${var.project}-elastic-cache-subnet-group-${var.environment}-${element(split(".", var.parameter_group_name), 1)}"
  subnet_ids = var.subnet_ids
}


resource "aws_cloudwatch_log_group" "redis_slow_log" {
  name = var.log_group_slow_log_name
  tags = var.tags
}

resource "aws_cloudwatch_log_group" "redis_engine_log" {
  name = var.log_group_engine_log_name
  tags = var.tags
}

resource "aws_elasticache_replication_group" "this" {
  replication_group_id          = var.replication_group_id
  replication_group_description = "Replication Group for ElastiCache Cluster"
  node_type                     = var.node_type
  parameter_group_name          = var.parameter_group_name
  port                          = 6379
  automatic_failover_enabled    = true
  auto_minor_version_upgrade    = false
  subnet_group_name             = aws_elasticache_subnet_group.this.name
  security_group_ids            = var.security_group_ids
  apply_immediately             = true
  multi_az_enabled              = var.multi_az_enabled
  engine_version                = var.engine_version
  engine                        = var.engine_type
  notification_topic_arn        = "arn:aws:sns:${var.region}:${var.account_id}:${var.project}-balance-alerts-${var.environment}"

  cluster_mode {
    replicas_per_node_group = var.replicas_per_node_group
    num_node_groups         = 1
  }

  log_delivery_configuration {
    destination      = aws_cloudwatch_log_group.redis_slow_log.name
    destination_type = "cloudwatch-logs"
    log_format       = "text"
    log_type         = "slow-log"
  }

  log_delivery_configuration {
    destination      = aws_cloudwatch_log_group.redis_engine_log.name
    destination_type = "cloudwatch-logs"
    log_format       = "text"
    log_type         = "engine-log"
  }

  tags = var.tags
}