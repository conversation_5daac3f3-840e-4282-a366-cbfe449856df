variable "availability_zones" {
  type        = list(any)
  description = "AZs for Redis Cluster"
}

variable "environment" {
  description = "Environment where this resource is deployed"
}

variable "node_type" {
  type        = string
  description = "Node Type for Redis Cluster nodes"
}

variable "project" {
  description = "Project that owns this resource"
}

variable "security_group_ids" {
  type        = list(any)
  description = "Security group ids for Redis Cluster"
}

variable "subnet_ids" {
  type        = list(any)
  description = "Subnet IDs for Redis Cluster"
}

variable "tags" {
  type = map(any)
}

variable "parameter_group_name" {
  type        = string
  description = "parameter group name"
}

variable "multi_az_enabled" {
  type        = bool
  description = "Multi AZ enabled or not"
}

variable "account_id" {
  type        = string
  description = "AWS account number"
}

variable "replicas_per_node_group" {
  type        = number
  description = "Number of replicas per node group"
}

variable "region" {
  type        = string
  description = "Region where this resource is deployed"
  default     = "ca-central-1"
}

variable "engine_version" {
  type        = string
  description = "Redis Engine Version"
}

variable "log_group_slow_log_name" {
  type        = string
  description = "Name of the slow log group"
}

variable "log_group_engine_log_name" {
  type        = string
  description = "Name of the engine log group"
}

variable "replication_group_id" {
  type        = string
  description = "Replication group id"
}

variable "engine_type" {
  type        = string
  description = "ElastiCache Engine Type"
  default     = "redis"
}