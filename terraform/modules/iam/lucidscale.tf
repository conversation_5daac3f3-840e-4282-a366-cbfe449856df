resource "aws_iam_role" "lucidscale_gl_read_only_prod" {
  count = var.environment == "prod" ? 1 : 0
  name  = "lucidscale_gl_read_only_prod"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::799803075172:root"
        },
        Action = "sts:AssumeRole",
        Condition = {
          StringEquals = {
            "sts:ExternalId" = "594caab4-7666-44e1-a356-3ac3d5c7d0d4"
          }
        }
      }
    ]
  })
}

resource "aws_iam_policy" "lucidscale_gl_read_only_prod" {
  count       = var.environment == "prod" ? 1 : 0
  name        = "lucidscale_gl_read_only_prod"
  description = "Lucidscale read-only access to infrastructure for prod"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "apigateway:GET",
          "appsync:ListDataSources",
          "appsync:ListGraphqlApis",
          "autoscaling:DescribeAutoScalingGroups",
          "autoscaling:DescribeLaunchConfigurations",
          "cloudfront:ListDistributions",
          "cloudfront:ListTagsForResource",
          "cloudtrail:DescribeTrails",
          "cloudtrail:ListTags",
          "cloudtrail:ListTrails",
          "cognito-idp:DescribeUserPool",
          "cognito-idp:ListUserPools",
          "dynamodb:DescribeTable",
          "dynamodb:ListTables",
          "dynamodb:ListTagsOfResource",
          "ec2:Describe*",
          "ecs:Describe*",
          "ecs:List*",
          "eks:DescribeCluster",
          "eks:ListClusters",
          "elasticfilesystem:Describe*",
          "elasticache:Describe*",
          "elasticloadbalancing:Describe*",
          "elasticmapreduce:DescribeCluster",
          "elasticmapreduce:ListClusters",
          "es:DescribeElasticsearchDomains",
          "es:ListDomainNames",
          "es:ListTags",
          "events:Describe*",
          "events:List*",
          "firehose:Describe*",
          "firehose:List*",
          "glacier:DescribeVault",
          "glacier:ListVaults",
          "iam:ListAccountAliases",
          "kafka:ListClustersV2",
          "kinesis:Describe*",
          "kinesis:List*",
          "lambda:List*",
          "network-firewall:DescribeFirewall",
          "network-firewall:ListFirewalls",
          "rds:Describe*",
          "rds:ListTagsForResource",
          "redshift:DescribeClusters",
          "route53:List*",
          "s3:GetBucket*",
          "s3:ListAllMyBuckets",
          "sns:Get*",
          "sns:List*",
          "sqs:GetQueueAttributes",
          "sqs:List*",
          "states:Describe*",
          "states:List*",
          "sts:GetCallerIdentity"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lucidscale_readonly_attach" {
  count      = var.environment == "prod" ? 1 : 0
  role       = aws_iam_role.lucidscale_gl_read_only_prod[count.index].name
  policy_arn = aws_iam_policy.lucidscale_gl_read_only_prod[count.index].arn
}