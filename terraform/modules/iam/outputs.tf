output "vpc_flow_logs_role_arn" {
  value = aws_iam_role.flow_logs.arn
}

output "vpc_eks_role_arn" {
  value = aws_iam_role.eks_main_master.arn
}

output "vpc_eks_role_main_node_arn" {
  value = aws_iam_role.eks_main_node.arn
}

output "ec2_instance_arn" {
  value = aws_iam_role.ec2_instance.arn
}

output "ec2_instance_profile_name" {
  value = aws_iam_instance_profile.ec2_instance_profile.name
}

output "eks_main_node_role" {
  value = aws_iam_role.eks_main_node.name
}

output "eks_main_node_name" {
  value = aws_iam_instance_profile.eks_main_node.name
}

output "eks_nodes_access_s3_bucket_common_services_arn" {
  value = one(aws_iam_policy.eks_nodes_access_s3_bucket_common_services[*].arn)
}

output "kubernetes_cloudwatch_logs_arn" {
  value = aws_iam_policy.kubernetes_cloudwatch_logs.arn
}

output "fluent_bit_iam_policy_arn" {
  value = aws_iam_policy.aws_for_fluent_bit.arn
}

output "aws_backup_role_arn" {
  value = aws_iam_role.aws_backup.arn
}

output "rds_automation_role_arn" {
  value = aws_iam_role.rds_start_stop.arn
}
