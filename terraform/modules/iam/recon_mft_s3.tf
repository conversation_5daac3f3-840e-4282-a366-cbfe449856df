# IAM Policy for MFT S3 access
resource "aws_iam_policy" "tol_recon_mft_s3_policy" {
  count       = var.environment == "prod" ? 0 : 1
  name        = "${var.tol_recon_mft_s3_name}-policy"
  description = "Policy for accessing MFT S3 bucket"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "AllowS3GlobalActions",
        Effect = "Allow",
        Action = [
          "s3:ListAllMyBuckets",
          "s3:GetBucketLocation"
        ],
        Resource = "*"
      },
      {
        Sid    = "AllowS3BucketOperations",
        Effect = "Allow",
        Action = [
          "s3:ListBucket",
          "s3:GetBucketAcl"
        ],
        Resource = [
          "arn:aws:s3:::${var.tol_recon_mft_s3_name}"
        ]
      },
      {
        Sid    = "AllowS3ObjectOperations",
        Effect = "Allow",
        Action = [
          "s3:AbortMultipartUpload",
          "s3:DeleteObject",
          "s3:GetObject",
          "s3:ListBucketMultipartUploads",
          "s3:ListMultipartUploadParts",
          "s3:PutObject",
          "s3:PutObjectAcl"
        ],
        Resource = [
          "arn:aws:s3:::${var.tol_recon_mft_s3_name}/*"
        ]
      }
    ]
  })
}

# IAM User Service Account
resource "aws_iam_user" "tol_recon_mft_sa" {
#   count = var.environment == "prod" ? 0 : 1
  name = "${var.tol_recon_mft_s3_name}-sa"
  path = "/service-accounts/"

  tags = {
    Description = "Service account for MFT reconciliation"
    Service     = "Reconciliation"
  }
}

# Attach the policy to the IAM user
resource "aws_iam_user_policy_attachment" "tol_recon_mft_s3_policy_attachment" {
  count       = var.environment == "prod" ? 0 : 1
  user       = aws_iam_user.tol_recon_mft_sa.name
  policy_arn = aws_iam_policy.tol_recon_mft_s3_policy[count.index].arn
}