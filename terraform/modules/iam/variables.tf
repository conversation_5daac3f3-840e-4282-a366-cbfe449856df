variable "account_id" {
  type        = string
  description = "Account ID where this resource is hosted"
}
variable "environment" {
  description = "Environment where this resource is deployed"
}

variable "project" {
  description = "Project that owns this resource"
}

variable "vpc_region" {
  description = "Region where VPC specific resources are deployed"
}

variable "tol_recon_mft_s3_name" {
  description = "Name of the S3 bucket for MFT reconciliation"
  type        = string
}