###################################
#PEOPLES DEVELOPER ACCESS NON PROD #
###################################

resource "aws_iam_policy" "peoples_developer_policy" {
  count       = var.environment == "qa" || var.environment == "staging" ? 1 : 0
  name        = "peoples-developer-access-policy"
  description = "Custom access for development support to production"
  path        = "/"
  policy = jsonencode(
    {
      Statement = [
        {
          Action = [
            "s3:ListAllMyBuckets",
            "s3:GetBucketLocation",
            "s3:ListBucket",
            "s3:ListBucketVersions",
            "s3:ListObjectsV2",
            "s3:PutObject",
            "ecr:CreateRepository",
            "eks:AccessKubernetesApi",
            "eks:DescribeCluster",
            "eks:AccessKubernetesApi",
            "cloudfront:CreateInvalidation",
            "athena:StartQueryExecution",
            "athena:GetQueryExecution",
            "athena:GetQueryResults",
            "athena:StopQueryExecution",
            "athena:ListQueryExecutions",
            "athena:ListWorkGroups",
            "athena:GetWorkGroup",
            "athena:ListDataCatalogs",
            "athena:ListDatabases",
            "athena:GetDatabase",
            "athena:ListTableMetadata",
            "athena:GetTableMetadata",
            "bedrock:InvokeModel",
            "bedrock:InvokeModelWithResponseStream",
            "lambda:InvokeFunctionUrl"
          ]
          Resource = "*"
          Sid      = "VisualEditor0"
          Effect   = "Allow"
        },
        {
          Action = [
            "s3:ListBucket",
          ]
          Resource = "*"
          Sid      = "VisualEditor1"
          Effect   = "Allow"
        },
      ]
      Version = "2012-10-17"
    }
  )
}

resource "aws_iam_role" "peoples_developer_role" {
  count                = var.environment == "qa" || var.environment == "staging" ? 1 : 0
  name                 = "AWSReservedSSO_Developer-Non-Prod_037bc659e36c54ac"
  path                 = "/aws-reserved/sso.amazonaws.com/ca-central-1/"
  max_session_duration = 43200
  description          = "Developer Access to non-production accounts. Create a policy \"peoples-developer-access-policy\" in local accounts to customize"
  assume_role_policy = jsonencode(
    {
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Effect" : "Allow",
          "Principal" : {
            "Federated" : "arn:aws:iam::************:saml-provider/AWSSSO_5f88a67472101425_DO_NOT_DELETE"
          },
          "Action" : [
            "sts:AssumeRoleWithSAML",
            "sts:TagSession"
          ],
          "Condition" : {
            "StringEquals" : {
              "SAML:aud" : "https://signin.aws.amazon.com/saml"
            }
          }
        }
      ]
    }
  )
  managed_policy_arns = [
    "arn:aws:iam::************:policy/peoples-developer-access-policy",
    "arn:aws:iam::aws:policy/AWSCodeBuildAdminAccess",
    "arn:aws:iam::aws:policy/AWSCodePipeline_FullAccess",
    "arn:aws:iam::aws:policy/AWSLambda_FullAccess",
    "arn:aws:iam::aws:policy/AmazonAPIGatewayAdministrator",
    "arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess",
    "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryPowerUser",
    "arn:aws:iam::aws:policy/CloudWatchFullAccess",
    "arn:aws:iam::aws:policy/ReadOnlyAccess",
    "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy",
  ]
}

resource "aws_iam_role_policy_attachment" "peoples_developer" {
  count      = var.environment == "qa" || var.environment == "staging" ? 1 : 0
  role       = aws_iam_role.peoples_developer_role[0].name
  policy_arn = aws_iam_policy.peoples_developer_policy[0].arn
}

resource "aws_iam_policy" "peoples_prod_developer_policy" {
  count       = var.environment == "prod" ? 1 : 0
  name        = "peoples-developer-access-policy"
  description = "Custom access for development support to production"
  path        = "/"
  policy = jsonencode(
    {
      Statement = [
        {
          Action = [
            "s3:ListAllMyBuckets",
            "s3:GetBucketLocation",
          ]
          Effect   = "Allow"
          Resource = "*"
          Sid      = "VisualEditor0"
        },
        {
          Action = [
            "s3:ListBucket",
          ]
          Effect   = "Allow"
          Resource = "*"
          Sid      = "VisualEditor1"
        },
        {
          Action = [
            "cloudwatch:PutDashboard",
            "cloudwatch:GetDashboard",
            "cloudwatch:ListDashboards",
          ]
          Effect   = "Allow"
          Resource = "*"
          Sid      = "VisualEditor2"
        },
        {
          Action = [
            "s3:GetObject",
            "s3:GetObjectVersion",
            "s3:PutObject",
          ]
          Effect   = "Allow"
          Resource = "*"
          Sid      = "VisualEditor3"
        },
        {
          Action = ["eks:DescribeCluster",
            "eks:DescribeNodegroup",
            "eks:ListClusters",
            "eks:ListNodegroups",
            "eks:AccessKubernetesApi",
          ]
          Effect   = "Allow"
          Resource = "arn:aws:eks:ca-central-1:************:cluster/*"
          Sid      = "VisualEditor4"
        },
      ]
      Version = "2012-10-17"
    }
  )
}

resource "aws_iam_role" "peoples_prod_developer_role" {
  count = var.environment == "prod" ? 1 : 0
  assume_role_policy = jsonencode(
    {
      Statement = [
        {
          Action = [
            "sts:AssumeRoleWithSAML",
            "sts:TagSession",
          ]
          Condition = {
            StringEquals = {
              "SAML:aud" = "https://signin.aws.amazon.com/saml"
            }
          }
          Effect = "Allow"
          Principal = {
            Federated = "arn:aws:iam::************:saml-provider/AWSSSO_d9eabd4ccdeb9b4b_DO_NOT_DELETE"
          }
        },
      ]
      Version = "2012-10-17"
    }
  )
  description           = "Controls developers' access to production accounts.\nCreate a policy \"peoples-developer-access-policy\" in local accounts to customize"
  force_detach_policies = false
  managed_policy_arns = [
    "arn:aws:iam::************:policy/peoples-developer-access-policy",
    "arn:aws:iam::aws:policy/ReadOnlyAccess",
  ]
  max_session_duration = 43200
  name                 = "AWSReservedSSO_Developers-production_126232b4856d12b5"
  path                 = "/aws-reserved/sso.amazonaws.com/ca-central-1/"
  tags                 = {}
  tags_all             = {}

  inline_policy {
    name = "AwsSSOInlinePolicy"
    policy = jsonencode(
      {
        Statement = [
          {
            Action = [
              "logs:PutMetricFilter",
              "logs:DescribeLogStreams",
            ]
            Effect = "Allow"
            Resource = [
              "arn:aws:logs:*:*:*",
            ]
          },
          {
            Action   = "ssm:*"
            Effect   = "Deny"
            Resource = "*"
            Sid      = "DenySSM"
          },
          {
            Action   = "secretsmanager:*"
            Effect   = "Deny"
            Resource = "*"
            Sid      = "DenySecrets"
          },
        ]
        Version = "2012-10-17"
      }
    )
  }
}

resource "aws_iam_role_policy_attachment" "peoples_prod_developer" {
  count      = var.environment == "prod" ? 1 : 0
  role       = aws_iam_role.peoples_prod_developer_role[0].name
  policy_arn = aws_iam_policy.peoples_prod_developer_policy[0].arn
}

##############################
# PEOPLES QA ACCESS NON PROD #
##############################

resource "aws_iam_policy" "peoples_qa_access_policy" {
  count       = var.environment == "qa" || var.environment == "staging" ? 1 : 0
  name        = "peoples-QA-access-policy"
  description = "Account specific QA access permissions"
  path        = "/"
  policy = jsonencode(
    {
      Statement = [
        {
          Action = [
            "acm:ExportCertificate",
            "acm:GetAccountConfiguration",
            "acm:DescribeCertificate",
            "acm:GetCertificate",
            "acm:ListCertificates",
            "acm:ListTagsForCertificate",
            "lambda:InvokeFunctionUrl"
          ]
          Effect   = "Allow"
          Resource = "*"
          Sid      = "VisualEditor0"
        },
        {
          Action = [
            "eks:AccessKubernetesApi",
            "logs:PutQueryDefinition",
          ]
          Effect = "Allow"
          Resource = [
            "*",
          ]
        },
        {
          Action = [
            "ssm:GetDocument",
            "ssm:SendCommand",
            "ssm:StartSession",
          ]
          Effect   = "Allow"
          Resource = "*"
        },
        {
          Action = [
            "ec2:StartInstances",
            "ec2:StopInstances",
          ]
          Effect = "Allow"
          Resource = [
            "arn:aws:ec2:ca-central-1:************:instance/i-0fb8f1aaf768c5b4b",
          ]
          Sid = "StartStopEC2"
        },
      ]
      Version = "2012-10-17"
    }
  )
}

#############
# FLOW LOGS #
#############

data "aws_iam_policy_document" "assume_role_policy_flow_logs" {
  statement {
    actions = [
      "sts:AssumeRole",
    ]

    principals {
      type = "Service"

      identifiers = [
        "vpc-flow-logs.amazonaws.com",
      ]
    }
  }
}

data "aws_iam_policy_document" "flow_logs" {
  statement {
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:DescribeLogGroups",
      "logs:DescribeLogStreams",
    ]

    resources = [
      "*",
    ]
  }
}

resource "aws_iam_policy" "flow_logs" {
  name        = "${var.project}-flow-logs-policy-${var.environment}"
  path        = "/"
  description = "Allows VPC flow logs to baas with cloudwatch as needed"
  policy      = data.aws_iam_policy_document.flow_logs.json
}

resource "aws_iam_role" "flow_logs" {
  name               = "${var.project}-flow-logs-role-${var.environment}"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy_flow_logs.json
}

resource "aws_iam_role_policy_attachment" "flow_logs" {
  role       = aws_iam_role.flow_logs.name
  policy_arn = aws_iam_policy.flow_logs.arn
}

#######################
# Interac DEVELOPER   #
#######################

data "aws_iam_policy_document" "interac_developer" {
  count = var.environment == "dev" ? 1 : 0

  statement {
    actions = [
      "iam:ListRoles",
      "iam:ListUsers",
      "iam:ListGroups",
      "iam:ListRolePolicies",
      "iam:GetPolicy",
      "iam:GetPolicyVersion",
      "iam:GetRole",
      "iam:GetRolePolicy",
      "iam:ListAttachedRolePolicies",
      "cloudwatch:Describe*",
      "cloudwatch:List*",
      "cloudwatch:Get*",
      "logs:Get*",
      "logs:Describe*",
      "logs:Filter*",
      "logs:List*",
    ]

    resources = [
      "*",
    ]
  }

  #S3 access 
  statement {
    actions = [
      "s3:GetBucketLocation",
      "s3:GetObject",
      "s3:ListBucket",
      "s3:ListBucketMultipartUploads",
      "s3:ListMultipartUploadParts",
      "s3:AbortMultipartUpload",
      "s3:CreateBucket",
      "s3:PutObject",
      "s3:GetBucketAcl",
    ]

    resources = [
      "arn:aws:s3:::com-${var.project}-*",
    ]
  }

  statement {
    actions = [
      "s3:ListAllMyBuckets",
      "s3:GetBucketLocation",
    ]

    resources = [
      "*",
    ]
  }

  # Block access to anything other than etl.tfstate
  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]

    effect = "Deny"

    resources = [
      "arn:aws:s3:::com-${var.project}-terraform-state/terraform.tfstate",
      "arn:aws:s3:::com-${var.project}-terraform-state/workspaces/*",
    ]
  }

  # ECR access

  statement {
    actions = [
      "ecr:GetDownloadUrlForLayer",
      "ecr:GetAuthorizationToken",
      "ecr:BatchGetImage",
      "ecr:BatchCheckLayerAvailability",
      "ecr:PutImage",
      "ecr:PutImageTagMutability",
      "ecr:InitiateLayerUpload",
      "ecr:UploadLayerPart",
      "ecr:CompleteLayerUpload",
      "ecr:DescribeRepositories",
      "ecr:GetRepositoryPolicy",
      "ecr:ListImages",
      "ecr:DeleteRepository",
      "ecr:BatchDeleteImage",
      "ecr:SetRepositoryPolicy",
      "ecr:DeleteRepositoryPolicy",
      "ecr:CreateRepository",
      "ecr:DescribeImages",
      "ecr:GetLifecyclePolicy",
      "ecr:GetLifecyclePolicyPreview",
      "ecr:ListTagsForResource",
      "ecr:DescribeImageScanFindings",
      "tag:GetResources",
    ]

    resources = [
      "*",
    ]
  }
  #ECS  
  statement {
    actions = [
      "ecs:*",
    ]

    resources = [
      "*",
    ]
  }
  statement {
    actions = [
      "elasticloadbalancing:Describe*",
      "elasticloadbalancing:DeregisterInstancesFromLoadBalancer",
      "elasticloadbalancing:RegisterInstancesWithLoadBalancer",
      "ec2:Describe*",
      "ec2:AuthorizeSecurityGroupIngress",
    ]

    resources = [
      "*",
    ]
  }
  #API GATEWAY    
  statement {
    actions = [
      "apigateway:*",
    ]

    resources = [
      "arn:aws:apigateway:*::/*",
    ]
  }

  statement {
    actions = [
      "lambda:*",
    ]

    resources = [
      "*",
    ]
  }

  statement {
    actions = [
      "eks:Get*",
      "eks:Describe*",
      "eks:List*",
    ]

    resources = [
      "*",
    ]
  }
}

resource "aws_iam_policy" "interac_developer" {
  count       = var.environment == "dev" ? 1 : 1
  name        = "${var.project}-developer-${var.environment}"
  path        = "/"
  description = "Allows Interac developers to perform configurations on the ECR"
  policy      = data.aws_iam_policy_document.interac_developer[0].json
}

################
#   EC2 ROLE   #
################
data "aws_iam_policy_document" "assume_role_policy_ec2" {
  statement {
    actions = [
      "sts:AssumeRole",
    ]

    principals {
      type = "Service"

      identifiers = [
        "ec2.amazonaws.com",
      ]
    }
  }
}

data "aws_iam_policy_document" "ec2_instance_for_ecs" {
  statement {
    actions = [
      "ecr:GetAuthorizationToken",
      "ecr:BatchCheckLayerAvailability",
      "ecr:GetDownloadUrlForLayer",
      "ecr:BatchGetImage",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["*"]
  }
}

data "aws_iam_policy_document" "ec2_ro_eks_nodes" {
  statement {
    actions = [
      "ec2:DescribeInstances",
      "ec2:DescribeAvailabilityZones",
      "ec2:DescribeInstanceStatus",
      "ec2:DescribeNetworkInterfaces",
      "ec2:DescribeNetworkInterfaceAttribute",
      "ec2:DescribeNetworkInterfacePermissions",
      "ec2:DescribeTags",
      "ec2:DescribeSubnets"
    ]
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "ec2_kafka_access" {
  statement {
    actions   = ["kafka-cluster:Connect", "kafka-cluster:AlterCluster", "kafka-cluster:DescribeCluster"]
    resources = ["arn:aws:kafka:region:${var.account_id}:cluster/${var.project}-kafka-cluster-${var.environment}/*"]
  }

  statement {
    actions   = ["kafka-cluster:*Topic*", "kafka-cluster:WriteData", "kafka-cluster:ReadData"]
    resources = ["arn:aws:kafka:region:${var.account_id}:topic/${var.project}-kafka-cluster-${var.environment}/*"]
  }

  statement {
    actions   = ["kafka-cluster:AlterGroup", "kafka-cluster:DescribeGroup"]
    resources = ["arn:aws:kafka:region:${var.account_id}:group/${var.project}-kafka-cluster-${var.environment}/*"]
  }
}
resource "aws_iam_policy" "ec2_instance" {
  name        = "${var.project}-ec2-instance-policy-${var.environment}"
  path        = "/"
  description = "Allows ECS for EC2 Instance"
  policy      = data.aws_iam_policy_document.ec2_instance_for_ecs.json
}

resource "aws_iam_policy" "ec2_kafka_access" {
  name        = "${var.project}-ec2-kafka-access-policy-${var.environment}"
  path        = "/"
  description = "Allows Kafka access for EC2 Instance"
  policy      = data.aws_iam_policy_document.ec2_kafka_access.json
}

resource "aws_iam_policy" "ec2_ro_eks_nodes" {
  name        = "${var.project}-ec2-ro-eks-nodes-policy-${var.environment}"
  path        = "/"
  description = "Allows ReadOnly access to EKS nodes for EC2 Instance"
  policy      = data.aws_iam_policy_document.ec2_ro_eks_nodes.json
}
resource "aws_iam_role" "ec2_instance" {
  name               = "${var.project}-ec2-instance-role-${var.environment}"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy_ec2.json
}

resource "aws_iam_role_policy_attachment" "ec2_instance" {
  role       = aws_iam_role.ec2_instance.name
  policy_arn = aws_iam_policy.ec2_instance.arn
}

resource "aws_iam_role_policy_attachment" "ec2_kafka_access" {
  role       = aws_iam_role.ec2_instance.name
  policy_arn = aws_iam_policy.ec2_kafka_access.arn
}

resource "aws_iam_role_policy_attachment" "ec2_ro_eks_nodes" {
  role       = aws_iam_role.ec2_instance.name
  policy_arn = aws_iam_policy.ec2_ro_eks_nodes.arn
}

resource "aws_iam_instance_profile" "ec2_instance_profile" {
  name = "${var.project}-ec2-instance-profile-${var.environment}"
  role = aws_iam_role.ec2_instance.name
}

################
#  EKS Cluster #
################

data "aws_iam_policy_document" "assume_role_policy_eks_master" {
  statement {
    actions = [
      "sts:AssumeRole",
    ]

    principals {
      type = "Service"

      identifiers = [
        "eks.amazonaws.com",
      ]
    }
  }
}

resource "aws_iam_role" "eks_main_master" {
  name               = "${var.project}-eks-main-cluster-role-${var.environment}"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy_eks_master.json
}

resource "aws_iam_role_policy_attachment" "eks_main_cluster_AmazonEKSClusterPolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"
  role       = aws_iam_role.eks_main_master.name
}

resource "aws_iam_role_policy_attachment" "eks_main_cluster_AmazonEKSServicePolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSServicePolicy"
  role       = aws_iam_role.eks_main_master.name
}

################
#  EKS Node    #
################

resource "aws_iam_role" "eks_main_node" {
  name               = "${var.project}-eks-main-node-role-${var.environment}"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy_ec2.json
}

resource "aws_iam_role_policy_attachment" "eks_main_node_AmazonEKSWorkerNodePolicy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy"
  role       = aws_iam_role.eks_main_node.name
}

resource "aws_iam_role_policy_attachment" "eks_main_node_AmazonEKS_CNI_Policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"
  role       = aws_iam_role.eks_main_node.name
}

resource "aws_iam_role_policy_attachment" "eks_main_node_AmazonEC2ContainerRegistryReadOnly" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
  role       = aws_iam_role.eks_main_node.name
}

resource "aws_iam_role_policy_attachment" "eks_main_node_AmazonEC2FullAccess" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2FullAccess"
  role       = aws_iam_role.eks_main_node.name
}

resource "aws_iam_instance_profile" "eks_main_node" {
  name = "${var.project}-eks-main-node-${var.environment}"
  role = aws_iam_role.eks_main_node.name
}

## External s3 access

data "aws_iam_policy_document" "external_s3_access" {
  statement {
    actions = [
      "s3:GetBucketLocation",
      "s3:ListAllMyBuckets",
    ]

    resources = [
      "*",
    ]
  }

  statement {
    actions = [
      "s3:ListBucket",
    ]

    resources = [
      "arn:aws:s3:::com-${var.project}-data-mft-${var.environment}",
    ]
  }

  statement {
    actions = [
      "s3:PutObject",
      "s3:GetObject",
      "s3:DeleteObject",
      "s3:ListObject",
      "s3:PutObjectAcl",
    ]

    resources = [
      "arn:aws:s3:::com-${var.project}-data-mft-${var.environment}/*",
    ]
  }
}

resource "aws_iam_policy" "external_s3_access" {
  name        = "${var.project}-external-s3-access-${var.environment}"
  path        = "/"
  description = "Attach to IAM users that need external access to fundstranfer s3 buckets"
  policy      = data.aws_iam_policy_document.external_s3_access.json
}


data "aws_iam_policy_document" "kubernetes_cloudwatch_logs" {
  statement {
    actions = [
      "logs:PutLogEvents",
      "logs:CreateLogGroup",
      "logs:PutRetentionPolicy",
      "logs:CreateLogStream",
      "logs:DescribeLogGroups",
      "logs:DescribeLogStreams",
    ]

    resources = [
      "*",
    ]
  }
}

resource "aws_iam_policy" "kubernetes_cloudwatch_logs" {
  name        = "${var.project}-kubernetes-cloudwatch-logs-policy-${var.environment}"
  path        = "/"
  description = "Policy for Kubernetes logs save to CloudWatch"
  policy      = data.aws_iam_policy_document.kubernetes_cloudwatch_logs.json
}

data "aws_iam_policy_document" "eks_nodes_access_s3_bucket_common_services" {
  statement {
    effect    = "Allow"
    actions   = ["sts:AssumeRole"]
    resources = ["arn:aws:iam::529566071206:role/access-from-another-acc-s3-common-services-software-packages"]
  }
}

resource "aws_iam_policy" "eks_nodes_access_s3_bucket_common_services" {
  count       = var.environment == "dev" ? 0 : 1
  name        = "${var.project}-eks-nodes-access-s3-common-services-${var.environment}"
  description = "EKS nodes instance profile policy to access S3 bucket in Common Services"
  policy      = data.aws_iam_policy_document.eks_nodes_access_s3_bucket_common_services.json
}

data "aws_iam_policy_document" "irsa" {
  statement {
    sid       = "PutLogEvents"
    effect    = "Allow"
    resources = ["arn:aws:logs:ca-central-1:${var.account_id}:log-group:*:log-stream:*"]
    actions   = ["logs:PutLogEvents"]
  }

  statement {
    sid       = "CreateCWLogs"
    effect    = "Allow"
    resources = ["arn:aws:logs:ca-central-1:${var.account_id}:log-group:*"]

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:DescribeLogGroups",
      "logs:DescribeLogStreams",
    ]
  }
  # statement {
  #   effect = "Allow"
  #   resources = var.environment == "prod" ? ["arn:aws:iam::************:role/GENERAL-LEDGER-PROD-CLOUDWATCH-ACCESS"] : ["arn:aws:iam::************:role/GENERAL-LEDGER-NON-PROD-CLOUDWATCH-ACCESS"]
  #   actions   = ["sts:AssumeRole"]
  # }
}

resource "aws_iam_policy" "aws_for_fluent_bit" {
  name        = "${upper(var.project)}-${upper(var.environment)}-fluentbit-log-access-policy"
  description = "IAM Policy for AWS for FluentBit"
  policy      = data.aws_iam_policy_document.irsa.json
}

# Deny developers to access Secrets and passwords in the Prod account
#data "aws_iam_policy_document" "deny_dev_to_see_pw" {
#  statement {
#    sid       = "DenySSM"
#    effect    = "Deny"
#    resources = ["*"]
#    actions   = [
#      "ssm:*"
#    ]
#  }
#
#  statement {
#    sid       = "DenySecrets"
#    effect    = "Deny"
#    resources = ["*"]
#
#    actions = [
#      "secretsmanager:*"
#    ]
#  }
#}
#resource "aws_iam_policy" "deny_dev_to_see_pw" {
#  count       = var.environment == "prod" ? 1 : 0
#  name        = "DenyDevToSeePasswords"
#  description = "Deny developers to see passwords in Prod"
#  policy      = data.aws_iam_policy_document.deny_dev_to_see_pw.json
#}
#
#resource "aws_iam_role_policy_attachment" "deny-policy-attach" {
#  count      = var.environment == "prod" ? 1 : 0
#  role       = "AWSReservedSSO_GL-PROD-DEVELOPER_41a911bda591e839"
#  policy_arn = aws_iam_policy.deny_dev_to_see_pw[count.index].arn
#}

##### AWS BACKUP #####
data "aws_iam_policy_document" "assume_role_policy_aws_backup" {
  statement {
    actions = [
      "sts:AssumeRole",
    ]

    principals {
      type = "Service"

      identifiers = [
        "backup.amazonaws.com",
      ]
    }
  }
}

resource "aws_iam_role_policy_attachment" "aws_backup" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup"
  role       = aws_iam_role.aws_backup.name
}

resource "aws_iam_role" "aws_backup" {
  name               = "${var.project}-aws-backup-${var.environment}"
  path               = "/"
  description        = "Grants AWS backup permissions to backup/restore EC2 servers"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy_aws_backup.json
}
#######################
# SSM RDS Start stop
#######################
data "aws_iam_policy_document" "assume_role_ssm" {
  statement {
    actions = [
      "sts:AssumeRole",
    ]

    principals {
      type = "Service"

      identifiers = [
        "ssm.amazonaws.com",
      ]
    }
  }
}

data "aws_iam_policy_document" "rds_start_stop" {
  statement {
    actions = [
      "rds:Describe*",
      "rds:Start*",
      "rds:Stop*",
      "rds:Reboot*"
    ]

    resources = [
      "*",
    ]
  }
}

resource "aws_iam_policy" "rds_start_stop" {
  name        = "${var.project}-rds-start-stop-${var.environment}"
  path        = "/"
  description = "Allows SSM to start/stop RDS instances"
  policy      = data.aws_iam_policy_document.rds_start_stop.json
}

resource "aws_iam_role" "rds_start_stop" {
  name               = "${var.project}-rds-start-stop-role-${var.environment}"
  assume_role_policy = data.aws_iam_policy_document.assume_role_ssm.json
}

resource "aws_iam_role_policy_attachment" "rds_start_stop" {
  role       = aws_iam_role.rds_start_stop.name
  policy_arn = aws_iam_policy.rds_start_stop.arn
}
