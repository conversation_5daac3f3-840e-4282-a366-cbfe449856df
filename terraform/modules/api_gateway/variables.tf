variable "certificate_arn" {
  type        = string
  description = "SSL Certificate ARN from ACM"
}

variable "domain_name" {
  type        = string
  description = "Domain name for etransfer api"
}

variable "environment" {
  type        = string
  description = "Environment containing these resources"
}

variable "customer_nlb_name" {
  type        = string
  description = "Network load balancer name for the Customer API"
}
variable "account_nlb_name" {
  type        = string
  description = "Network load balancer name for the Account API"
}
variable "transfer_nlb_name" {
  type        = string
  description = "Network load balancer name for the Transfer API"
}
variable "reference_nlb_name" {
  type        = string
  description = "Network load balancer name for the Reference API"
}
variable "oauth2_nlb_name" {
  type        = string
  description = "Network load balancer name for the OAuth2 API"
}
variable "project" {
  type        = string
  description = "Project owning these resources"
}
