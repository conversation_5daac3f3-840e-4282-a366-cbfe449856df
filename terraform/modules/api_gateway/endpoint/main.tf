terraform {
  experiments = [module_variable_optional_attrs]
}

resource "aws_api_gateway_resource" "this" {
  rest_api_id = var.rest_api_id
  parent_id   = var.parent_resource_id
  path_part   = var.resource
}

resource "aws_api_gateway_method" "this" {
  count = length(var.endpoint_spec)

  rest_api_id   = var.rest_api_id
  resource_id   = aws_api_gateway_resource.this.id
  http_method   = var.endpoint_spec[count.index]["Method"]
  authorization = var.endpoint_spec[count.index]["AuthorizationType"]
  authorizer_id = var.endpoint_spec[count.index]["AuthorizerId"]
  # authorization_scopes = var.endpoint_spec[count.index]["AuthorizationScopes"]

  request_parameters = var.method_request_params
}

resource "aws_api_gateway_method" "mock_this" {
  count = length(var.mock_endpoint_spec)

  rest_api_id   = var.rest_api_id
  resource_id   = aws_api_gateway_resource.this.id
  http_method   = var.mock_endpoint_spec[count.index]["Method"]
  authorization = var.mock_endpoint_spec[count.index]["AuthorizationType"]
  authorizer_id = var.mock_endpoint_spec[count.index]["AuthorizerId"]
}

resource "aws_api_gateway_integration" "this" {
  count = length(var.endpoint_spec)

  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[count.index].http_method

  type                    = var.endpoint_spec[count.index]["IntegrationType"]
  uri                     = var.endpoint_spec[count.index]["URI"]
  integration_http_method = var.endpoint_spec[count.index]["Method"]

  connection_type = var.endpoint_spec[count.index]["ConnectionType"]
  connection_id   = var.endpoint_spec[count.index]["ConnectionId"]

  request_parameters = var.integration_request_params
  # request_parameters = {"param"= "method.request.path.proxy"}
}

resource "aws_api_gateway_integration" "mock_this" {
  count = length(var.mock_endpoint_spec)

  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.mock_this[count.index].http_method

  type = "MOCK"

  request_templates = {
    "application/json" = <<EOF
    {"statusCode": 200}
    EOF
  }
}


resource "aws_api_gateway_method_response" "response_200" {
  count       = length(var.mock_endpoint_spec)
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.mock_this[count.index].http_method
  status_code = 200

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "mock_this" {
  count       = length(var.mock_endpoint_spec)
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.mock_this[count.index].http_method
  status_code = aws_api_gateway_method_response.response_200[count.index].status_code

  # Transforms the backend JSON response to XML
  response_templates = {
    "application/json" = var.mock_endpoint_spec[count.index]["MockResponse"]
  }

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
}

# CORS

resource "aws_api_gateway_method" "events_options" {
  rest_api_id   = var.rest_api_id
  resource_id   = aws_api_gateway_resource.this.id
  http_method   = "OPTIONS"
  authorization = "NONE"

  request_parameters = var.method_request_params
}

resource "aws_api_gateway_integration" "events_options" {
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.events_options.http_method

  type = "MOCK"

  request_parameters = var.integration_request_params

  request_templates = {
    "application/json" = "{statusCode:200}"
  }
}

resource "aws_api_gateway_method_response" "events_options_response_200" {
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.events_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "events_options_200" {
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.events_options.http_method
  status_code = aws_api_gateway_method_response.events_options_response_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
}
