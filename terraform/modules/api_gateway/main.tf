resource "aws_api_gateway_account" "gl-qa-api-gateway" {
  cloudwatch_role_arn = aws_iam_role.cloudwatch.arn
}

resource "aws_iam_role" "cloudwatch" {
  name = "api_gateway_cloudwatch_global"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Service": "apigateway.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "cloudwatch" {
  name = "api_gateway_push_logs"
  role = aws_iam_role.cloudwatch.id

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:DescribeLogGroups",
                "logs:DescribeLogStreams",
                "logs:PutLogEvents",
                "logs:GetLogEvents",
                "logs:FilterLogEvents"
            ],
            "Resource": "*"
        }
    ]
}
EOF
}
resource "aws_api_gateway_domain_name" "domain" {
  certificate_arn = var.certificate_arn
  domain_name     = var.domain_name
  security_policy = "TLS_1_2"
}
resource "aws_api_gateway_base_path_mapping" "this" {
  api_id      = aws_api_gateway_rest_api.api.id
  stage_name  = upper(var.environment)
  domain_name = aws_api_gateway_domain_name.domain.domain_name

  depends_on = [aws_api_gateway_stage.stage]
}
resource "aws_api_gateway_deployment" "deployment" {
  rest_api_id = aws_api_gateway_rest_api.api.id
}
resource "aws_api_gateway_stage" "stage" {
  stage_name    = upper(var.environment)
  deployment_id = aws_api_gateway_deployment.deployment.id
  rest_api_id   = aws_api_gateway_rest_api.api.id
}
data "aws_lb" "customer_nlb" {
  name = var.customer_nlb_name
}
data "aws_lb" "account_nlb" {
  name = var.account_nlb_name
}
data "aws_lb" "transfer_nlb" {
  name = var.transfer_nlb_name
}
data "aws_lb" "reference_nlb" {
  name = var.reference_nlb_name
}
data "aws_lb" "oauth2_nlb" {
  name = var.oauth2_nlb_name
}
resource "aws_api_gateway_vpc_link" "customer" {
  name        = "${var.project}-customer-vpc-link-${var.environment}"
  description = "VPC link for Authorization service in ${var.environment}"
  target_arns = [data.aws_lb.customer_nlb.arn]
}
resource "aws_api_gateway_vpc_link" "account" {
  name        = "${var.project}-account-vpc-link-${var.environment}"
  description = "VPC link for Authorization service in ${var.environment}"
  target_arns = [data.aws_lb.account_nlb.arn]
}
resource "aws_api_gateway_vpc_link" "transfer" {
  name        = "${var.project}-transfer-vpc-link-${var.environment}"
  description = "VPC link for Authorization service in ${var.environment}"
  target_arns = [data.aws_lb.transfer_nlb.arn]
}
resource "aws_api_gateway_vpc_link" "reference" {
  name        = "${var.project}-reference-vpc-link-${var.environment}"
  description = "VPC link for Authorization service in ${var.environment}"
  target_arns = [data.aws_lb.reference_nlb.arn]
}
resource "aws_api_gateway_vpc_link" "oauth2" {
  name        = "${var.project}-oauth2-vpc-link-${var.environment}"
  description = "VPC link for Authorization service in ${var.environment}"
  target_arns = [data.aws_lb.oauth2_nlb.arn]
}

resource "aws_api_gateway_rest_api" "api" {
  name        = "${var.project}-api-${var.environment}"
  description = "Request for payment from an individual or organization"
}
resource "aws_api_gateway_resource" "v1" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "v1"
}

module "customer" {
  source = "./endpoint"

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = aws_api_gateway_resource.v1.id
  resource           = "customer"

  endpoint_spec = [
    {
      Method            = "ANY"
      AuthorizationType = "NONE"
      AuthorizerId      = ""
      # AuthorizationType = "COGNITO_USER_POOLS"
      # AuthorizerId = aws_api_gateway_authorizer.cognito.id
      IntegrationType     = "HTTP_PROXY"
      URI                 = "http://${data.aws_lb.customer_nlb.dns_name}:8080/v1/customer"
      ConnectionType      = "VPC_LINK"
      ConnectionId        = aws_api_gateway_vpc_link.customer.id
      AuthorizationScopes = ["com.cfsb.ach/publicRestApi.all"]
    }
  ]

  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-cfsb-service-account,x-cfsb-interaction-id,x-cfsb-interaction-timestamp'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}
module "account" {
  source = "./endpoint"

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = aws_api_gateway_resource.v1.id
  resource           = "account"

  endpoint_spec = [
    {
      Method            = "ANY"
      AuthorizationType = "NONE"
      AuthorizerId      = ""
      # AuthorizationType = "COGNITO_USER_POOLS"
      # AuthorizerId = aws_api_gateway_authorizer.cognito.id
      IntegrationType     = "HTTP_PROXY"
      URI                 = "http://${data.aws_lb.account_nlb.dns_name}:8080/v1/account"
      ConnectionType      = "VPC_LINK"
      ConnectionId        = aws_api_gateway_vpc_link.account.id
      AuthorizationScopes = ["com.cfsb.ach/publicRestApi.all"]
    }
  ]

  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-cfsb-service-account,x-cfsb-interaction-id,x-cfsb-interaction-timestamp'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}
module "transfer" {
  source = "./endpoint"

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = aws_api_gateway_resource.v1.id
  resource           = "transfer"

  endpoint_spec = [
    {
      Method            = "ANY"
      AuthorizationType = "NONE"
      AuthorizerId      = ""
      # AuthorizationType = "COGNITO_USER_POOLS"
      # AuthorizerId = aws_api_gateway_authorizer.cognito.id
      IntegrationType     = "HTTP_PROXY"
      URI                 = "http://${data.aws_lb.transfer_nlb.dns_name}:8080/v1/transfer"
      ConnectionType      = "VPC_LINK"
      ConnectionId        = aws_api_gateway_vpc_link.transfer.id
      AuthorizationScopes = ["com.cfsb.ach/publicRestApi.all"]
    }
  ]

  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-cfsb-service-account,x-cfsb-interaction-id,x-cfsb-interaction-timestamp'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}
module "reference" {
  source = "./endpoint"

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = aws_api_gateway_resource.v1.id
  resource           = "reference"

  endpoint_spec = [
    {
      Method            = "ANY"
      AuthorizationType = "NONE"
      AuthorizerId      = ""
      # AuthorizationType = "COGNITO_USER_POOLS"
      # AuthorizerId = aws_api_gateway_authorizer.cognito.id
      IntegrationType     = "HTTP_PROXY"
      URI                 = "http://${data.aws_lb.reference_nlb.dns_name}:8080/v1/reference"
      ConnectionType      = "VPC_LINK"
      ConnectionId        = aws_api_gateway_vpc_link.reference.id
      AuthorizationScopes = ["com.cfsb.ach/publicRestApi.all"]
    }
  ]

  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-cfsb-service-account,x-cfsb-interaction-id,x-cfsb-interaction-timestamp'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}

module "oauth2" {
  source = "./endpoint"

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = aws_api_gateway_rest_api.api.root_resource_id
  resource           = "oauth2"

  endpoint_spec = [
    {
      Method            = "ANY"
      AuthorizationType = "NONE"
      AuthorizerId      = ""
      # AuthorizationType = "COGNITO_USER_POOLS"
      # AuthorizerId = aws_api_gateway_authorizer.cognito.id
      IntegrationType     = "HTTP_PROXY"
      URI                 = "http://${data.aws_lb.oauth2_nlb.dns_name}:8080/oauth2/token"
      ConnectionType      = "VPC_LINK"
      ConnectionId        = aws_api_gateway_vpc_link.oauth2.id
      AuthorizationScopes = ["com.cfsb.ach/publicRestApi.all"]
    }
  ]

  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-cfsb-service-account,x-cfsb-interaction-id,x-cfsb-interaction-timestamp'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}

module "customer_proxy" {
  source = "./endpoint"

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = module.customer.resource_id
  resource           = "{proxy+}"

  endpoint_spec = [
    {
      Method            = "ANY"
      AuthorizationType = "NONE"
      AuthorizerId      = ""
      # AuthorizationType = "COGNITO_USER_POOLS"
      # AuthorizerId = aws_api_gateway_authorizer.cognito.id
      IntegrationType     = "HTTP_PROXY"
      URI                 = "http://${data.aws_lb.customer_nlb.dns_name}:8080/v1/customer/{proxy}"
      ConnectionType      = "VPC_LINK"
      ConnectionId        = aws_api_gateway_vpc_link.customer.id
      AuthorizationScopes = ["com.cfsb.ach/publicRestApi.all"]
    }
  ]
  method_request_params = {
    "method.request.path.proxy" = true
  }
  integration_request_params = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-cfsb-service-account,x-cfsb-interaction-id,x-cfsb-interaction-timestamp'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}
module "account_proxy" {
  source = "./endpoint"

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = module.account.resource_id
  resource           = "{proxy+}"

  endpoint_spec = [
    {
      Method            = "ANY"
      AuthorizationType = "NONE"
      AuthorizerId      = ""
      # AuthorizationType = "COGNITO_USER_POOLS"
      # AuthorizerId = aws_api_gateway_authorizer.cognito.id
      IntegrationType     = "HTTP_PROXY"
      URI                 = "http://${data.aws_lb.account_nlb.dns_name}:8080/v1/account/{proxy}"
      ConnectionType      = "VPC_LINK"
      ConnectionId        = aws_api_gateway_vpc_link.account.id
      AuthorizationScopes = ["com.cfsb.ach/publicRestApi.all"]
    }
  ]
  method_request_params = {
    "method.request.path.proxy" = true
  }
  integration_request_params = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-cfsb-service-account,x-cfsb-interaction-id,x-cfsb-interaction-timestamp'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}
module "transfer_proxy" {
  source = "./endpoint"

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = module.transfer.resource_id
  resource           = "{proxy+}"

  endpoint_spec = [
    {
      Method            = "ANY"
      AuthorizationType = "NONE"
      AuthorizerId      = ""
      # AuthorizationType = "COGNITO_USER_POOLS"
      # AuthorizerId = aws_api_gateway_authorizer.cognito.id
      IntegrationType     = "HTTP_PROXY"
      URI                 = "http://${data.aws_lb.transfer_nlb.dns_name}:8080/v1/transfer/{proxy}"
      ConnectionType      = "VPC_LINK"
      ConnectionId        = aws_api_gateway_vpc_link.transfer.id
      AuthorizationScopes = ["com.cfsb.ach/publicRestApi.all"]
    }
  ]
  method_request_params = {
    "method.request.path.proxy" = true
  }
  integration_request_params = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-cfsb-service-account,x-cfsb-interaction-id,x-cfsb-interaction-timestamp'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}
module "reference_proxy" {
  source = "./endpoint"

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = module.reference.resource_id
  resource           = "{proxy+}"

  endpoint_spec = [
    {
      Method            = "ANY"
      AuthorizationType = "NONE"
      AuthorizerId      = ""
      # AuthorizationType = "COGNITO_USER_POOLS"
      # AuthorizerId = aws_api_gateway_authorizer.cognito.id
      IntegrationType     = "HTTP_PROXY"
      URI                 = "http://${data.aws_lb.reference_nlb.dns_name}:8080/v1/reference/{proxy}"
      ConnectionType      = "VPC_LINK"
      ConnectionId        = aws_api_gateway_vpc_link.reference.id
      AuthorizationScopes = ["com.cfsb.ach/publicRestApi.all"]
    }
  ]
  method_request_params = {
    "method.request.path.proxy" = true
  }
  integration_request_params = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-cfsb-service-account,x-cfsb-interaction-id,x-cfsb-interaction-timestamp'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}


module "oauth2_proxy" {
  source = "./endpoint"

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = module.oauth2.resource_id
  resource           = "{proxy+}"

  endpoint_spec = [
    {
      Method            = "ANY"
      AuthorizationType = "NONE"
      AuthorizerId      = ""
      # AuthorizationType = "COGNITO_USER_POOLS"
      # AuthorizerId = aws_api_gateway_authorizer.cognito.id
      IntegrationType     = "HTTP_PROXY"
      URI                 = "http://${data.aws_lb.oauth2_nlb.dns_name}:8080/oauth2/token"
      ConnectionType      = "VPC_LINK"
      ConnectionId        = aws_api_gateway_vpc_link.oauth2.id
      AuthorizationScopes = ["com.cfsb.ach/publicRestApi.all"]
    }
  ]
  method_request_params = {
    "method.request.path.proxy" = true
  }
  integration_request_params = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-cfsb-service-account,x-cfsb-interaction-id,x-cfsb-interaction-timestamp'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}
