#data "aws_iam_policy_document" "irsa" {
#  statement {
#    sid       = "PutLogEvents"
#    effect    = "Allow"
#    resources = ["arn:aws:logs:ca-central-1:${var.account_id}:log-group:*:log-stream:*"]
#    actions   = ["logs:PutLogEvents"]
#  }
#
#  statement {
#    sid       = "CreateCWLogs"
#    effect    = "Allow"
#    resources = ["arn:aws:logs:ca-central-1:${var.account_id}:log-group:*"]
#
#    actions = [
#      "logs:CreateLogGroup",
#      "logs:CreateLogStream",
#      "logs:DescribeLogGroups",
#      "logs:DescribeLogStreams",
#    ]
#  }
#}
#
#resource "aws_iam_policy" "aws_for_fluent_bit" {
#  name        = "${upper(var.project)}-${upper(var.environment)}-fluentbit-log-access-policy"
#  description = "IAM Policy for AWS for FluentBit"
#  policy      = data.aws_iam_policy_document.irsa.json
#}
