resource "kubernetes_service_account_v1" "aws-lb-controller" {
# count = var.environment == "dev" ? 0 : 1
  metadata {
    name = "aws-load-balancer-controller-${var.environment}"
    namespace = "kube-system"
    labels = {
      "app.kubernetes.io/component" = "controller"
      "app.kubernetes.io/name" = "aws-load-balancer-controller"
    }
    annotations = {
      "eks.amazonaws.com/role-arn" = "${aws_iam_role.lb_controller.arn}"
      "alb.ingress.kubernetes.io/tags" = "${var.project}-eks"
    }
  }
}
