variable "create_management_namespace" {
  default = true
}
variable "create_namespace" {
  default = true
}
variable "service_names" {
  type = map(string)
}
variable "domain_name" {
  type = string
}
variable "project" {
  type = string
}
variable "environment" {
  type = string
}
variable "oidc_provider" {
  type = string
}
variable "aws_account" {
  type = string
}
variable "tags" {
  type = map(string)
}
variable "namespace" {
  type = string
}
#variable "ecr_repo_names" {
#  type = list(string)
#}
