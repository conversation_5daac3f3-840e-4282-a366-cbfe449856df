#resource "kubernetes_namespace" "namespace" {
#  count = var.create_namespace ? 1 : 0
#  metadata {
#    name = "pg-${var.project}-${var.environment}"
#  }
#}
#
#resource "kubernetes_namespace" "mgmt_namespace" {
#  count = var.create_management_namespace ? 1 : 0
#  metadata {
#    name = "pg-${var.project}-mgmt"
#  }
#}

resource "kubernetes_ingress_v1" "ledger_ingress" {
  metadata {
    name = "ledger-ingress"
#   namespace = kubernetes_namespace.namespace.0.metadata.0.name
    namespace = var.namespace
    annotations = {
      "kubernetes.io/ingress.class" = "alb"
      "alb.ingress.kubernetes.io/scheme" = "internal"
      "alb.ingress.kubernetes.io/target-type" = "ip"
      "alb.ingress.kubernetes.io/group.name" = "shared"
      "alb.ingress.kubernetes.io/group.order" = 5
      "alb.ingress.kubernetes.io/listen-ports" = "[{\"HTTP\": 8080}]"
      "alb.ingress.kubernetes.io/healthcheck-path" = "/actuator/health"
    }
  }
    spec {
      rule {
         host = var.domain_name
         http {

          dynamic "path" {
          for_each = var.service_names
            content {
             path_type = "Prefix"
             backend {
               service {
                 name = "${path.value}"
                 port {
                   number = 8080
                 }
               }
             }
             path = "${path.key}"
            }
          }
        }
      }
      rule {
         host = var.environment == "qa" ? "ledger-qas-util-api.peoplescloud.io" : "ledger-stg-util-api.peoplescloud.io"
         http {
           path {
           path_type = "Prefix"
           backend {
             service {
                name = "${var.environment}-util-service"
                port {
                    number = 8080
                }
             }
           }
           path = "/v1/util"
         }
        }
      }
    }
}
