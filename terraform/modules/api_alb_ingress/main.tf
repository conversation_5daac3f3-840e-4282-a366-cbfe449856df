resource "aws_api_gateway_account" "gl-qa-api-gateway" {
  cloudwatch_role_arn = aws_iam_role.cloudwatch.arn
}
resource "aws_iam_role" "cloudwatch" {
  name = "api_gateway_cloudwatch_${var.environment}"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Service": "apigateway.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}
resource "aws_iam_role_policy" "cloudwatch" {
  name = "api_gateway_push_logs"
  role = aws_iam_role.cloudwatch.id

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:DescribeLogGroups",
                "logs:DescribeLogStreams",
                "logs:PutLogEvents",
                "logs:GetLogEvents",
                "logs:FilterLogEvents"
            ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_api_gateway_domain_name" "domain" {
  certificate_arn = var.certificate_arn
  domain_name     = var.domain_name
  security_policy = "TLS_1_2"
}

module "waf" {
  source = "./waf"
  count  = var.enable_waf ? 1 : 0

  name = "${var.project}-${var.environment}-api-gw"
  waf_resource_arns = {
    "PG-SYSTEM"       = aws_api_gateway_stage.stage.arn,
    "ADMIN-UI"        = aws_api_gateway_stage.admin_ui_stage.arn,
    "EXTERNAL-CLIENT" = aws_api_gateway_stage.external_client_stage.arn
  }
  limit_per_ip = var.environment == "staging" ? 65000 : 20000
  project      = var.project
  environment  = var.environment

  tags = var.tags
}
