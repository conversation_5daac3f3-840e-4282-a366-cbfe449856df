locals {
  api_mapping = yamldecode(var.api_res_meth_mappings)

  path_args = { for path, resource in local.api_mapping :
    path => { for index, value in resource.methods :
      value => {
        Method              = value,
        AuthorizationType   = "AWS_IAM",
        AuthorizerId        = "",
        IntegrationType     = "HTTP",
        URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger.path}/${path}",
        ConnectionType      = "VPC_LINK",
        ConnectionId        = aws_api_gateway_vpc_link.api_gw_vpc_link_nlb_connector_for_alb.id,
        AuthorizationScopes = []
      }
    } if path == "transaction"
  }

  proxy_args = { for path, resource in local.api_mapping :
    path => { for index, value in resource.proxy.methods :
      value => {
        Method              = value,
        AuthorizationType   = "AWS_IAM",
        AuthorizerId        = "",
        IntegrationType     = "HTTP",
        URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger.path}/${path}/{proxy}",
        ConnectionType      = "VPC_LINK",
        ConnectionId        = aws_api_gateway_vpc_link.api_gw_vpc_link_nlb_connector_for_alb.id,
        AuthorizationScopes = []
      }
    } if path == "transaction"
  }

  async_args = [for path, resource in local.api_mapping :
    { for async_key, async_value in resource :
      async_key => { for index, value in async_value.methods :
        value => {
          Method              = value,
          AuthorizationType   = "AWS_IAM",
          AuthorizerId        = "",
          IntegrationType     = "HTTP",
          URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger.path}/${path}/async",
          ConnectionType      = "VPC_LINK",
          ConnectionId        = aws_api_gateway_vpc_link.api_gw_vpc_link_nlb_connector_for_alb.id,
          AuthorizationScopes = []
        }
      } if async_key == "async"
    } if path == "transaction"
  ]

  async_proxy_args = { for path, resource in local.api_mapping :
    path => { for index, value in resource.async.proxy.methods :
      value => {
        Method              = value,
        AuthorizationType   = "AWS_IAM",
        AuthorizerId        = "",
        IntegrationType     = "HTTP",
        URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger.path}/${path}/async/{proxy}",
        ConnectionType      = "VPC_LINK",
        ConnectionId        = aws_api_gateway_vpc_link.api_gw_vpc_link_nlb_connector_for_alb.id,
        AuthorizationScopes = []
      }
    } if path == "transaction"
  }
}

resource "aws_api_gateway_base_path_mapping" "this" {
  api_id      = aws_api_gateway_rest_api.api.id
  stage_name  = aws_api_gateway_stage.stage.stage_name
  domain_name = aws_api_gateway_domain_name.domain.domain_name
  base_path   = "internal"

  depends_on = [aws_api_gateway_stage.stage]
}
resource "aws_api_gateway_rest_api" "api" {
  name                         = "${var.project}-pg-system-api-${var.environment}"
  description                  = "${title(var.project)} PG System API for ${var.environment}"
  disable_execute_api_endpoint = true
}
resource "aws_api_gateway_deployment" "deployment" {
  rest_api_id = aws_api_gateway_rest_api.api.id

  lifecycle {
    create_before_destroy = true
  }
}
resource "aws_api_gateway_stage" "stage" {
  stage_name           = upper(var.environment)
  deployment_id        = aws_api_gateway_deployment.deployment.id
  rest_api_id          = aws_api_gateway_rest_api.api.id
  xray_tracing_enabled = true


  lifecycle {
    ignore_changes = [deployment_id]
  }
}
resource "aws_api_gateway_method_settings" "all" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  stage_name  = aws_api_gateway_stage.stage.stage_name
  method_path = "*/*"

  settings {
    metrics_enabled    = true
    logging_level      = "INFO"
    data_trace_enabled = var.data_trace
  }
}
resource "aws_api_gateway_resource" "v1" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "v1"
}
resource "aws_api_gateway_resource" "ledger" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.v1.id
  path_part   = "ledger"
}

module "services_api_endpoints" {
  source = "./endpoint"

  for_each = local.path_args

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = aws_api_gateway_resource.ledger.id
  resource           = each.key

  endpoint_spec = each.value
  method_request_params = {
    "method.request.header.x-pg-interaction-id"         = true
    "method.request.header.x-pg-interaction-timestamp"  = true
    "method.request.header.x-pg-profile-id"             = each.key == "account" || each.key == "transaction" ? true : false
    "method.request.header.x-pg-account-id"             = each.key == "transaction" ? true : false
    "method.request.header.x-pg-amz-request-id"         = true
    "method.request.header.x-datadog-trace-id"          = true
    "method.request.header.x-datadog-parent-id"         = true
    "method.request.header.x-datadog-sampling-priority" = true
    "method.request.header.x-datadog-origin"            = true
    "method.request.header.x-datadog-tags"              = true
  }
  integration_request_params = {
    "integration.request.header.x-pg-interaction-id"         = "method.request.header.x-pg-interaction-id"
    "integration.request.header.x-pg-interaction-timestamp"  = "method.request.header.x-pg-interaction-timestamp"
    "integration.request.header.x-pg-profile-id"             = "method.request.header.x-pg-profile-id"
    "integration.request.header.x-pg-account-id"             = "method.request.header.x-pg-account-id"
    "integration.request.header.x-pg-amz-request-id"         = "context.requestId"
    "integration.request.header.x-datadog-trace-id"          = "method.request.header.x-datadog-trace-id"
    "integration.request.header.x-datadog-parent-id"         = "method.request.header.x-datadog-parent-id"
    "integration.request.header.x-datadog-sampling-priority" = "method.request.header.x-datadog-sampling-priority"
    "integration.request.header.x-datadog-origin"            = "method.request.header.x-datadog-origin"
    "integration.request.header.x-datadog-tags"              = "method.request.header.x-datadog-tags"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-account-id,x-pg-amz-request-id,x-datadog-trace-id,x-datadog-parent-id,x-datadog-sampling-priority,x-datadog-origin,x-datadog-tags'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}
module "endpoints_proxy" {
  source = "./endpoint"

  for_each = local.proxy_args

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = module.services_api_endpoints[each.key].resource_id
  resource           = "{proxy+}"

  endpoint_spec = each.value
  method_request_params = {
    "method.request.path.proxy"                        = true
    "method.request.header.x-pg-interaction-id"        = true
    "method.request.header.x-pg-interaction-timestamp" = true
    "method.request.header.x-pg-profile-id"            = each.key == "account" || each.key == "transaction" ? true : false
    "method.request.header.x-pg-account-id"            = each.key == "transaction" ? true : false
    "method.request.header.x-pg-amz-request-id"        = true
  }
  integration_request_params = {
    "integration.request.path.proxy"                        = "method.request.path.proxy"
    "integration.request.header.x-pg-interaction-id"        = "method.request.header.x-pg-interaction-id"
    "integration.request.header.x-pg-interaction-timestamp" = "method.request.header.x-pg-interaction-timestamp"
    "integration.request.header.x-pg-profile-id"            = "method.request.header.x-pg-profile-id"
    "integration.request.header.x-pg-account-id"            = "method.request.header.x-pg-account-id"
    "integration.request.header.x-pg-amz-request-id"        = "context.requestId"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-account-id,x-pg-amz-request-id'"
  cors_allowed_methods = "'GET,OPTIONS,POST,PATCH,PUT,DELETE'"
  cors_allowed_origins = "'*'"
}

module "async_api_endpoints" {
  source = "./endpoint"

  for_each = element(local.async_args, 0)

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = module.services_api_endpoints["transaction"].resource_id
  resource           = each.key

  endpoint_spec = each.value
  method_request_params = {
    "method.request.header.x-pg-interaction-id"         = true
    "method.request.header.x-pg-interaction-timestamp"  = true
    "method.request.header.x-pg-profile-id"             = true
    "method.request.header.x-pg-account-id"             = true
    "method.request.header.x-pg-amz-request-id"         = true
    "method.request.header.x-datadog-trace-id"          = true
    "method.request.header.x-datadog-parent-id"         = true
    "method.request.header.x-datadog-sampling-priority" = true
    "method.request.header.x-datadog-origin"            = true
    "method.request.header.x-datadog-tags"              = true

  }
  integration_request_params = {
    "integration.request.header.x-pg-interaction-id"         = "method.request.header.x-pg-interaction-id"
    "integration.request.header.x-pg-interaction-timestamp"  = "method.request.header.x-pg-interaction-timestamp"
    "integration.request.header.x-pg-profile-id"             = "method.request.header.x-pg-profile-id"
    "integration.request.header.x-pg-account-id"             = "method.request.header.x-pg-account-id"
    "integration.request.header.x-pg-amz-request-id"         = "context.requestId"
    "integration.request.header.x-datadog-trace-id"          = "method.request.header.x-datadog-trace-id"
    "integration.request.header.x-datadog-parent-id"         = "method.request.header.x-datadog-parent-id"
    "integration.request.header.x-datadog-sampling-priority" = "method.request.header.x-datadog-sampling-priority"
    "integration.request.header.x-datadog-origin"            = "method.request.header.x-datadog-origin"
    "integration.request.header.x-datadog-tags"              = "method.request.header.x-datadog-tags"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-account-id,x-pg-amz-request-id'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}
module "async_proxy" {
  source = "./endpoint"

  for_each = local.async_proxy_args

  rest_api_id        = aws_api_gateway_rest_api.api.id
  parent_resource_id = module.async_api_endpoints["async"].resource_id
  resource           = "{proxy+}"

  endpoint_spec = each.value
  method_request_params = {
    "method.request.path.proxy"                        = true
    "method.request.header.x-pg-interaction-id"        = true
    "method.request.header.x-pg-interaction-timestamp" = true
    "method.request.header.x-pg-profile-id"            = true
    "method.request.header.x-pg-account-id"            = true
    "method.request.header.x-pg-amz-request-id"        = true
  }
  integration_request_params = {
    "integration.request.path.proxy"                        = "method.request.path.proxy"
    "integration.request.header.x-pg-interaction-id"        = "method.request.header.x-pg-interaction-id"
    "integration.request.header.x-pg-interaction-timestamp" = "method.request.header.x-pg-interaction-timestamp"
    "integration.request.header.x-pg-profile-id"            = "method.request.header.x-pg-profile-id"
    "integration.request.header.x-pg-account-id"            = "method.request.header.x-pg-account-id"
    "integration.request.header.x-pg-amz-request-id"        = "context.requestId"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-account-id,x-pg-amz-request-id,x-datadog-trace-id,x-datadog-parent-id,x-datadog-sampling-priority,x-datadog-origin,x-datadog-tags'"
  cors_allowed_methods = "'GET,OPTIONS,POST,PATCH,PUT,DELETE'"
  cors_allowed_origins = "'*'"
}

