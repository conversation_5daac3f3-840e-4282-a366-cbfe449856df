
# Don't pay attention on number "*2*" in names. It is needed for blue/green deploy during changes
# This is the only one NLB for API Gateway

resource "aws_lb" "api_gw_nlb2" {
  name                             = "${var.project}-nlb-2-${var.environment}"
  internal                         = true
  load_balancer_type               = "network"
  enable_cross_zone_load_balancing = true


  subnet_mapping {
    subnet_id = var.private_subnet_ids[1]
  }
  subnet_mapping {
    subnet_id = var.private_subnet_ids[0]
  }

  # access_logs {
  #   bucket  = var.access_logs_bucket_name
  #   enabled = false
  # }

  enable_deletion_protection = false

  tags = merge({
    Environment = var.environment
    Project     = var.project
  }, var.environment != "prod" ? { "DatadogAllowed" = "false" } : {})
}

resource "aws_lb_target_group" "ingress_alb_2" {
  name        = "${var.project}-k8s-alb-tg-2-${var.environment}"
  target_type = "alb"
  port        = 8080
  protocol    = "TCP"
  vpc_id      = var.vpc_id

  health_check {
    enabled = true
    path    = "/"
  }
}

resource "aws_lb_listener" "ingress_alb_for_nlb2" {
  load_balancer_arn = aws_lb.api_gw_nlb2.arn
  port              = "80"
  protocol          = "TCP"

  tags = var.tags

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.ingress_alb_2.arn
  }


}

data "aws_lb" "alb_ingress_controller_2" {
  count = var.alb_hostname != "" ? 1 : 0
  name  = var.alb_hostname != "" ? split(".", var.alb_hostname)[0] : ""
}

resource "aws_lb_target_group_attachment" "ingress_alb_2" {
  count            = var.alb_hostname != "" ? 1 : 0
  target_group_arn = aws_lb_target_group.ingress_alb_2.arn
  target_id        = data.aws_lb.alb_ingress_controller_2[0].arn
}

resource "aws_api_gateway_vpc_link" "api_gw_vpc_link_nlb_connector_for_alb" {
  name        = "${var.project}-api-gateway-vpc-link-${var.environment}"
  description = "API gateway VPC link for NLB in ${var.environment}"
  target_arns = [data.aws_lb.agw_nlb_2.arn]
  tags        = var.tags

  depends_on = [
    aws_lb.api_gw_nlb2
  ]
}

data "aws_lb" "agw_nlb_2" {
  name = aws_lb.api_gw_nlb2.name
}