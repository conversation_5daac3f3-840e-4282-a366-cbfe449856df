terraform {
  experiments = [module_variable_optional_attrs]
}

resource "aws_api_gateway_resource" "this" {
  rest_api_id = var.rest_api_id
  parent_id   = var.parent_resource_id
  path_part   = var.resource
}

resource "aws_api_gateway_method" "this" {
  for_each = var.endpoint_spec

  rest_api_id          = var.rest_api_id
  resource_id          = aws_api_gateway_resource.this.id
  http_method          = each.value["Method"]
  authorization        = each.value["AuthorizationType"]
  authorizer_id        = each.value["AuthorizerId"]
  authorization_scopes = each.value["AuthorizationScopes"]

  request_parameters = var.method_request_params
}

resource "aws_api_gateway_integration" "this" {
  for_each = var.endpoint_spec

  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method

  type                    = each.value["IntegrationType"]
  uri                     = each.value["URI"]
  integration_http_method = each.value["Method"]

  connection_type = each.value["ConnectionType"]
  connection_id   = each.value["ConnectionId"]

  request_parameters = var.integration_request_params
}

resource "aws_api_gateway_method_response" "response_200_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 200

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = aws_api_gateway_method_response.response_200_this[each.key].status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
}

resource "aws_api_gateway_method_response" "response_201_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 201

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_201_this" {
  for_each          = var.endpoint_spec
  rest_api_id       = var.rest_api_id
  resource_id       = aws_api_gateway_resource.this.id
  http_method       = aws_api_gateway_method.this[each.key].http_method
  status_code       = aws_api_gateway_method_response.response_201_this[each.key].status_code
  selection_pattern = "201"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
}

resource "aws_api_gateway_method_response" "response_204_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 204

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_204_this" {
  for_each          = var.endpoint_spec
  rest_api_id       = var.rest_api_id
  resource_id       = aws_api_gateway_resource.this.id
  http_method       = aws_api_gateway_method.this[each.key].http_method
  status_code       = aws_api_gateway_method_response.response_204_this[each.key].status_code
  selection_pattern = "204"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
}

resource "aws_api_gateway_method_response" "response_400_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 400

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_400_this" {
  for_each          = var.endpoint_spec
  rest_api_id       = var.rest_api_id
  resource_id       = aws_api_gateway_resource.this.id
  http_method       = aws_api_gateway_method.this[each.key].http_method
  status_code       = aws_api_gateway_method_response.response_400_this[each.key].status_code
  selection_pattern = "400"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
}

resource "aws_api_gateway_method_response" "response_401_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 401

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_401_this" {
  for_each          = var.endpoint_spec
  rest_api_id       = var.rest_api_id
  resource_id       = aws_api_gateway_resource.this.id
  http_method       = aws_api_gateway_method.this[each.key].http_method
  status_code       = aws_api_gateway_method_response.response_401_this[each.key].status_code
  selection_pattern = "401"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
  response_templates = {
    "application/json" = jsonencode({
      message = "Unauthorized"
    })
  }
}

resource "aws_api_gateway_method_response" "response_404_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 404

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_404_this" {
  for_each          = var.endpoint_spec
  rest_api_id       = var.rest_api_id
  resource_id       = aws_api_gateway_resource.this.id
  http_method       = aws_api_gateway_method.this[each.key].http_method
  status_code       = aws_api_gateway_method_response.response_404_this[each.key].status_code
  selection_pattern = "404"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }

  response_templates = {
    "application/json" = <<-EOF
{
  "error": [
     {
        "message": "Not Found"
      }
  ]
}
EOF
  }
}

resource "aws_api_gateway_method_response" "response_405_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 405

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_405_this" {
  for_each          = var.endpoint_spec
  rest_api_id       = var.rest_api_id
  resource_id       = aws_api_gateway_resource.this.id
  http_method       = aws_api_gateway_method.this[each.key].http_method
  status_code       = aws_api_gateway_method_response.response_405_this[each.key].status_code
  selection_pattern = "405"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
}

resource "aws_api_gateway_method_response" "response_413_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 413

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_413_this" {
  for_each          = var.endpoint_spec
  rest_api_id       = var.rest_api_id
  resource_id       = aws_api_gateway_resource.this.id
  http_method       = aws_api_gateway_method.this[each.key].http_method
  status_code       = aws_api_gateway_method_response.response_413_this[each.key].status_code
  selection_pattern = "413"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
}
resource "aws_api_gateway_method_response" "response_500_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 500

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_500_this" {
  for_each          = var.endpoint_spec
  rest_api_id       = var.rest_api_id
  resource_id       = aws_api_gateway_resource.this.id
  http_method       = aws_api_gateway_method.this[each.key].http_method
  status_code       = aws_api_gateway_method_response.response_500_this[each.key].status_code
  selection_pattern = "500"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
  response_templates = {
    "application/json" = <<-EOF
     {
        "error": [
      {
        "message": "UNEXPECTED_ERROR"
      }
        ]
      }
EOF
  }

}

resource "aws_api_gateway_method_response" "response_503_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 503

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_503_this" {
  for_each          = var.endpoint_spec
  rest_api_id       = var.rest_api_id
  resource_id       = aws_api_gateway_resource.this.id
  http_method       = aws_api_gateway_method.this[each.key].http_method
  status_code       = aws_api_gateway_method_response.response_503_this[each.key].status_code
  selection_pattern = "503"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
  response_templates = {
    "application/json" = <<-EOF
      {
        "error": [
      {
        "message": "SERVICE_UNAVAILABLE"
      }
        ]
      }
      EOF
  }
}


resource "aws_api_gateway_method_response" "response_504_this" {
  for_each    = var.endpoint_spec
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.this[each.key].http_method
  status_code = 504

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_504_this" {
  for_each          = var.endpoint_spec
  rest_api_id       = var.rest_api_id
  resource_id       = aws_api_gateway_resource.this.id
  http_method       = aws_api_gateway_method.this[each.key].http_method
  status_code       = aws_api_gateway_method_response.response_504_this[each.key].status_code
  selection_pattern = "504"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
  response_templates = {
    "application/json" = <<-EOF
      {
        "error": [
      {
        "message": "Gateway Timeout"
      }
        ]
      }
      EOF
  }
}

# CORS

resource "aws_api_gateway_method" "events_options" {
  rest_api_id   = var.rest_api_id
  resource_id   = aws_api_gateway_resource.this.id
  http_method   = "OPTIONS"
  authorization = "NONE"

  request_parameters = var.method_request_params
}

resource "aws_api_gateway_integration" "events_options" {
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.events_options.http_method

  type = "MOCK"

  request_parameters = var.integration_request_params

  request_templates = {
    "application/json" = "{statusCode:200}"
  }
}

resource "aws_api_gateway_method_response" "events_options_response_200" {
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.events_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "events_options_200" {
  rest_api_id = var.rest_api_id
  resource_id = aws_api_gateway_resource.this.id
  http_method = aws_api_gateway_method.events_options.http_method
  status_code = aws_api_gateway_method_response.events_options_response_200.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = var.cors_allowed_headers,
    "method.response.header.Access-Control-Allow-Methods" = var.cors_allowed_methods,
    "method.response.header.Access-Control-Allow-Origin"  = var.cors_allowed_origins
  }
}
