# variable "endpoint_spec" {
#   type = map(object({
#     Method              = string
#     AuthorizationType   = string
#     AuthorizerId        = string
#     IntegrationType     = string
#     URI                 = string
#     ConnectionType      = string
#     ConnectionId        = string
#     AuthorizationScopes = optional(list(string))
#   }))

#   default = { "GET" = {
#     Method              = "POST"
#     AuthorizationType   = "CUSTOM"
#     AuthorizerId        = ""
#     IntegrationType     = "HTTP_PROXY"
#     URI                 = ""
#     ConnectionType      = "VPC_LINK"
#     ConnectionId        = ""
#     AuthorizationScopes = null
#   } }
# }

variable "rest_api_id" {
  description = "ID of the rest API resource"
}

variable "parent_resource_id" {
  description = "Parent API resource for the resource being created"
}

variable "resource" {
  description = "Name of the Rest resource, will create a path part"
}

variable "endpoint_spec" {
  type = map(object({
    Method              = string
    AuthorizationType   = string
    AuthorizerId        = string
    IntegrationType     = string
    URI                 = optional(string)
    ConnectionType      = optional(string)
    ConnectionId        = optional(string)
    AuthorizationScopes = optional(list(string))
  }))

  default = { "GET" = {
    Method              = "POST"
    AuthorizationType   = "CUSTOM"
    AuthorizerId        = ""
    IntegrationType     = "HTTP_PROXY"
    URI                 = ""
    ConnectionType      = "VPC_LINK"
    ConnectionId        = ""
    AuthorizationScopes = null
  } }
}

variable "mock_endpoint_spec" {
  type        = list(map(string))
  description = "Specification of the API endpoints"

  default = []
}

variable "method_request_params" {
  type        = map(string)
  description = "Parameters for allowing cors according to terraform spec"
  default     = {}
}

variable "integration_request_params" {
  type        = map(string)
  description = "Parameters for allowing cors according to terraform spec"
  default     = {}
}

variable "cors_allowed_headers" {
  type        = string
  description = "allowed headers for cors"
  default     = ""
}

variable "cors_allowed_methods" {
  type        = string
  description = "allowed methods for cors"
  default     = ""
}

variable "cors_allowed_origins" {
  type        = string
  description = "allowed origins for cors"
  default     = ""
}
