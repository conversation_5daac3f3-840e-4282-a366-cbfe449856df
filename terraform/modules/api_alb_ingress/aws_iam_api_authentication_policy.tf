resource "aws_iam_user" "postman" {
  name = "Postman-${upper(var.environment)}"
  path = "/system/"

  #tags = {
  #  "AKIA3UI2E66LDRXZNBVB" = "Postman-QA-keys" 
  #}
  #tags_all = {
  #  "AKIA3UI2E66LDRXZNBVB" = "Postman-QA-keys"
  #}
}
resource "aws_iam_user" "e-transfer" {
  name = "E-Transfer-${upper(var.environment)}"
  path = "/system/"

}

resource "aws_iam_group" "pg-system-clients" {
  name = "PG-SYSTEM-CLIENTS-${upper(var.environment)}"
  path = "/system/"
}
resource "aws_iam_group_membership" "pg-system-clients" {
  name = "pg-system-clients-group-membership"

  users = [
    aws_iam_user.postman.name,
    aws_iam_user.e-transfer.name,
  ]

  group = aws_iam_group.pg-system-clients.name
}

resource "aws_iam_group_policy_attachment" "api-auth-attach" {
  group      = aws_iam_group.pg-system-clients.name
  policy_arn = aws_iam_policy.api_authentication.arn
}

#resource "aws_iam_policy_attachment" "api-auth-attach" {
#  name       = "api-authentication-attachment"
#  users      = [aws_iam_user.postman.name]
#  policy_arn = aws_iam_policy.api_authentication.arn
#}

resource "aws_iam_policy" "api_authentication" {
  name        = "${upper(var.project)}-${upper(var.environment)}-api-authentication-policy"
  path        = "/"
  description = "AWS_IAM API authentication policy"

  # Terraform's "jsonencode" function converts a
  # Terraform expression result to valid JSON syntax.
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "execute-api:Invoke",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:execute-api:ca-central-1:${var.aws_account}:${aws_api_gateway_rest_api.api.id}/*/GET/v1/ledger/transaction",
          "arn:aws:execute-api:ca-central-1:${var.aws_account}:${aws_api_gateway_rest_api.api.id}/*/OPTIONS/v1/ledger/transaction",
          "arn:aws:execute-api:ca-central-1:${var.aws_account}:${aws_api_gateway_rest_api.api.id}/*/POST/v1/ledger/transaction",
          "arn:aws:execute-api:ca-central-1:${var.aws_account}:${aws_api_gateway_rest_api.api.id}/*/GET/v1/ledger/transaction/*",
          "arn:aws:execute-api:ca-central-1:${var.aws_account}:${aws_api_gateway_rest_api.api.id}/*/OPTIONS/v1/ledger/transaction/*",
          "arn:aws:execute-api:ca-central-1:${var.aws_account}:${aws_api_gateway_rest_api.api.id}/*/PATCH/v1/ledger/transaction/*",
          "arn:aws:execute-api:ca-central-1:${var.aws_account}:${aws_api_gateway_rest_api.api.id}/*/POST/v1/ledger/transaction/*",
          "arn:aws:execute-api:ca-central-1:${var.aws_account}:${aws_api_gateway_rest_api.api.id}/*/DELETE/v1/ledger/transaction/*",
        ]
      },
    ]
  })
}

