resource "aws_lb" "nlb_for_util" {
  count              = var.create_util_nlb ? 1 : 0
  name               = var.util_lb_name
  internal           = false
  load_balancer_type = "network"
  subnets            = var.public_subnet_ids

  enable_deletion_protection = true

  tags = {
    "DatadogAllowed" = "false"
  }
}

data "aws_lb" "nlb_for_util" {
  count = var.create_util_nlb && var.create_util_tg ? 1 : 0
  name  = "${var.project}-util"
}
resource "aws_lb_listener" "https" {
  count             = var.create_util_tg ? 1 : 0
  load_balancer_arn = var.create_util_nlb && var.create_util_tg ? aws_lb.nlb_for_util[count.index].arn : try(data.aws_lb.nlb_for_util[0].arn, "")
  port              = var.create_util_nlb && var.create_util_tg ? "80" : "81"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.util_alb[count.index].arn
  }
}

resource "aws_lb_target_group" "util_alb" {
  count       = var.create_util_tg ? 1 : 0
  name        = "${var.project}-util-alb-tg-${var.environment}"
  target_type = "alb"
  port        = 8080
  protocol    = "TCP"
  vpc_id      = var.vpc_id

  health_check {
    path = "/actuator/health"
  }
}

data "aws_lb" "util_alb_ingress" {
  count = var.create_util_tg ? 1 : 0
  name  = var.alb_hostname
}
resource "aws_lb_target_group_attachment" "util_alb" {
  count            = var.create_util_tg ? 1 : 0
  target_group_arn = aws_lb_target_group.util_alb[count.index].arn
  target_id        = data.aws_lb.util_alb_ingress[0].id
}
