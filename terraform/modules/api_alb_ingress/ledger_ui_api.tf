locals {
  admin_ui_api_mapping = yamldecode(var.admin_ui_meth_mappings)

  admin_ui_path_args = { for path, resource in local.admin_ui_api_mapping :
    path => { for index, value in resource.methods :
      value => {
        Method            = value,
        AuthorizationType = "COGNITO_USER_POOLS",
        AuthorizerId      = aws_api_gateway_authorizer.admin_ui_cognito.id,
        #AuthorizerId        = "",
        IntegrationType = "HTTP_PROXY",
        #       URI                 = "http://${aws_lb.api_gw_nlb.dns_name}:80${aws_api_gateway_resource.ledger.path}/${path}",
        URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger.path}/${path}",
        ConnectionType      = "VPC_LINK",
        ConnectionId        = aws_api_gateway_vpc_link.api_gw_vpc_link_nlb_connector_for_alb.id,
        AuthorizationScopes = ["openid", "email", "com.peoplesgroup.ledger.%{if var.environment == "staging"}stg%{else}${var.environment}%{endif}/adminUI.all"]
      }
    }
  }


  admin_ui_proxy_args = { for path, resource in local.admin_ui_api_mapping :
    path => { for index, value in resource.proxy.methods :
      value => {
        Method            = value,
        AuthorizationType = "COGNITO_USER_POOLS",
        AuthorizerId      = aws_api_gateway_authorizer.admin_ui_cognito.id,
        #AuthorizerId        = "",
        IntegrationType = "HTTP_PROXY",
        #       URI                 = "http://${aws_lb.api_gw_nlb.dns_name}:80${aws_api_gateway_resource.ledger.path}/${path}/{proxy}",
        URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger.path}/${path}/{proxy}",
        ConnectionType      = "VPC_LINK",
        ConnectionId        = aws_api_gateway_vpc_link.api_gw_vpc_link_nlb_connector_for_alb.id,
        AuthorizationScopes = ["openid", "email", "com.peoplesgroup.ledger.%{if var.environment == "staging"}stg%{else}${var.environment}%{endif}/adminUI.all"]
      }
    }
  }

}

resource "aws_api_gateway_rest_api" "admin_ui_api" {
  name                         = "${var.project}-admin-ui-api-${var.environment}"
  description                  = "${title(var.project)} AdminUI API for ${var.environment}"
  disable_execute_api_endpoint = true
}
resource "aws_api_gateway_base_path_mapping" "admin_ui" {
  api_id      = aws_api_gateway_rest_api.admin_ui_api.id
  stage_name  = aws_api_gateway_stage.admin_ui_stage.stage_name
  domain_name = aws_api_gateway_domain_name.domain.domain_name
  base_path   = "admin-ui"

  # depends_on = [aws_api_gateway_stage.stage]
}
resource "aws_api_gateway_deployment" "admin_ui_deployment" {
  rest_api_id = aws_api_gateway_rest_api.admin_ui_api.id

  lifecycle {
    create_before_destroy = true
  }
}
resource "aws_api_gateway_stage" "admin_ui_stage" {
  stage_name           = "UI-${upper(var.environment)}"
  deployment_id        = aws_api_gateway_deployment.admin_ui_deployment.id
  rest_api_id          = aws_api_gateway_rest_api.admin_ui_api.id
  xray_tracing_enabled = true

  lifecycle {
    ignore_changes = [deployment_id]
  }
}
resource "aws_api_gateway_method_settings" "admin_ui_all" {
  rest_api_id = aws_api_gateway_rest_api.admin_ui_api.id
  stage_name  = aws_api_gateway_stage.admin_ui_stage.stage_name
  method_path = "*/*"

  settings {
    metrics_enabled    = true
    logging_level      = "INFO"
    data_trace_enabled = var.data_trace
  }
}
resource "aws_api_gateway_authorizer" "admin_ui_cognito" {
  name                             = "${var.project}-admin-ui-authorizer-${var.environment}"
  rest_api_id                      = aws_api_gateway_rest_api.admin_ui_api.id
  authorizer_result_ttl_in_seconds = 0
  type                             = "COGNITO_USER_POOLS"
  provider_arns                    = [var.cognito_user_pool_arn]
}
#resource "aws_api_gateway_resource" "admin_ui" {
#  rest_api_id = aws_api_gateway_rest_api.admin_ui_api.id
#  parent_id   = aws_api_gateway_rest_api.admin_ui_api.root_resource_id
#  path_part   = "internal"
#}
resource "aws_api_gateway_resource" "admin_ui_v1" {
  rest_api_id = aws_api_gateway_rest_api.admin_ui_api.id
  parent_id   = aws_api_gateway_rest_api.admin_ui_api.root_resource_id
  path_part   = "v1"
}
resource "aws_api_gateway_resource" "admin_ui_ledger" {
  rest_api_id = aws_api_gateway_rest_api.admin_ui_api.id
  parent_id   = aws_api_gateway_resource.admin_ui_v1.id
  path_part   = "ledger"
}

module "admin_ui_api_endpoints" {
  source = "./endpoint"

  for_each = local.admin_ui_path_args

  rest_api_id        = aws_api_gateway_rest_api.admin_ui_api.id
  parent_resource_id = aws_api_gateway_resource.admin_ui_ledger.id
  resource           = each.key

  endpoint_spec = each.value
  method_request_params = {
  }
  integration_request_params = {
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-service-account,x-pg-profile-name,x-pg-profile-id,x-pg-signature,x-pg-amz-request-id,x-pg-signature-key-id,x-pg-signature-type,x-pg-account-id,account-id'"
  cors_allowed_methods = "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'"
  cors_allowed_origins = "'*'"
}

module "admin_ui_endpoints_proxy" {
  source = "./endpoint"

  for_each = local.admin_ui_proxy_args

  rest_api_id        = aws_api_gateway_rest_api.admin_ui_api.id
  parent_resource_id = module.admin_ui_api_endpoints[each.key].resource_id
  resource           = "{proxy+}"

  endpoint_spec = each.value
  method_request_params = {
    "method.request.path.proxy" = true
  }
  integration_request_params = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-service-account,x-pg-profile-name,x-pg-profile-id,x-pg-signature,x-pg-amz-request-id,x-pg-signature-key-id,x-pg-signature-type,x-pg-account-id,account-id'"
  cors_allowed_methods = "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'"
  cors_allowed_origins = "'*'"
}
