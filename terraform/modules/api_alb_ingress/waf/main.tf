locals {
  _default_managed_rules = [
    {
      name                                     = "AWS-AWSManagedRulesCommonRuleSet"
      priority                                 = 1
      managed_rule_group_statement_name        = "AWSManagedRulesCommonRuleSet"
      managed_rule_group_statement_vendor_name = "AWS"
      metric_name                              = "AWS-AWSManagedRulesCommonRuleSet"
    },
    {
      name                                     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
      priority                                 = 2
      managed_rule_group_statement_name        = "AWSManagedRulesKnownBadInputsRuleSet"
      managed_rule_group_statement_vendor_name = "AWS"
      metric_name                              = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    }
  ]

  # Maps name used for ruleset to a list of rule names that should be disabled in the ruleset
  _default_managed_rule_exclusions = {
    "AWS-AWSManagedRulesCommonRuleSet" = ["EC2MetaDataSSRF_BODY", "GenericRFI_BODY", "SizeRestrictions_BODY", "NoUserAgent_HEADER"]
  }

  _default_permitted_statements = [
    {
      positional_constraint = "EXACTLY"
      transformation        = "LOWERCASE"
      search_string         = "/"
      field_to_match        = "uri_path"
    },
    {
      positional_constraint = "STARTS_WITH"
      transformation        = "LOWERCASE"
      search_string         = "/"
      field_to_match        = "uri_path"
    }
  ]
}

locals {
  managed_rules           = var.managed_rules == null ? local._default_managed_rules : var.managed_rules
  managed_rule_exclusions = var.managed_rule_exclusions == null ? local._default_managed_rule_exclusions : var.managed_rule_exclusions
  permitted_statements    = var.permitted_statements == null ? local._default_permitted_statements : var.permitted_statements
}

resource "aws_wafv2_web_acl" "waf" {
  name        = var.name
  description = "WAF acl for ${var.name}"
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  rule {
    name     = "${var.name}-rule"
    priority = length(toset(local.managed_rules)) + 1
    action {
      block {}
    }

    statement {
      rate_based_statement {
        limit = var.limit_per_ip
        aggregate_key_type = "IP"
        scope_down_statement {
          or_statement {
            dynamic "statement" {
              for_each = toset(local.permitted_statements)
              content {
                byte_match_statement {
                  field_to_match {
                    dynamic "uri_path" {
                      for_each = statement.value.field_to_match == "uri_path" ? [1] : []
                      content {}
                    }
                    dynamic "method" {
                      for_each = statement.value.field_to_match == "method" ? [1] : []
                      content {}
                    }
                    dynamic "query_string" {
                      for_each = statement.value.field_to_match == "query_string" ? [1] : []
                      content {}
                    }
                    dynamic "body" {
                      for_each = statement.value.field_to_match == "body" ? [1] : []
                      content {}
                    }
                    dynamic "all_query_arguments" {
                      for_each = statement.value.field_to_match == "all_query_arguments" ? [1] : []
                      content {}
                    }
                  }
                  search_string = statement.value.search_string
                  text_transformation {
                    priority = 0
                    type     = statement.value.transformation
                  }
                  positional_constraint = statement.value.positional_constraint
                }
              }
            }
          }
        }
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "${var.name}-rule"
      sampled_requests_enabled   = true
    }
  }


  dynamic "rule" {
    for_each = toset(local.managed_rules)

    content {
      name     = rule.value.name
      priority = rule.value.priority
      override_action {
        none {}
      }
      statement {
        managed_rule_group_statement {
          name        = rule.value.managed_rule_group_statement_name
          vendor_name = rule.value.managed_rule_group_statement_vendor_name

          # If the managed_rule_exclusions has a key matching the name given to this rule set then apply that list of rule exclusions
          # to the rule set
          dynamic "excluded_rule" {
            for_each = contains(keys(local.managed_rule_exclusions), rule.value.name) ? local.managed_rule_exclusions[rule.value.name] : []
            content {
              name = excluded_rule.value
            }
          }
        }
      }
      visibility_config {
        cloudwatch_metrics_enabled = true
        metric_name                = rule.value.metric_name
        sampled_requests_enabled   = true
      }
    }
  }

  tags = var.tags

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "${var.name}_count"
    sampled_requests_enabled   = true
  }
}

resource "aws_wafv2_web_acl_association" "association" {
  for_each = var.waf_resource_arns

  resource_arn = each.value
  web_acl_arn  = aws_wafv2_web_acl.waf.arn
}
