variable "waf_resource_arns" {
  type = map(string)
}
variable "name" {
  type = string
}
variable "project" {
  type = string
}
variable "environment" {
  type = string
}
variable "tags" {
  type = map(string)
}
variable "managed_rules" {
  type        = list
  default     = null
  description = "Optional. Ability to specify list of managed rule sets. Defaults defined by local.default_managed_rules and are substitued explicitly in TF for the reasons explained in that code"
}
variable "managed_rule_exclusions" {
  type        = map
  default     = null
  description = "Optional. Ability to specify list of default exclusions to specify which rules in the specified sets of managed_rules get switched off. Defaults defined by local.default_managed_rules and are substitued explicitly in TF for the reasons explained in that code"
}
variable "permitted_statements" {
  type        = list
  default     = null
  description = "Optional. Actual defaults defined by local.default_permitted_statements and are substitued explicitly in TF for the reasons explained in that code"
}
variable "limit_per_ip" {
  default = 10000
}
