locals {
  cfsb_test_api_mapping = yamldecode(var.api_res_meth_mappings)

  cfsb_test_path_args = { for path, resource in local.cfsb_test_api_mapping :
    path => { for index, value in resource.methods :
      value => {
        Method = value,
        #AuthorizationType   = "COGNITO_USER_POOLS",
        #AuthorizerId        = aws_api_gateway_authorizer.cfsb_test_cognito.id,
        AuthorizationType = "NONE",
        AuthorizerId      = "",
        IntegrationType   = "HTTP",
        #       URI                 = "http://${aws_lb.api_gw_nlb.dns_name}:80${aws_api_gateway_resource.ledger.path}/${path}",
        URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger.path}/${path}",
        ConnectionType      = "VPC_LINK",
        ConnectionId        = aws_api_gateway_vpc_link.api_gw_vpc_link_nlb_connector_for_alb.id,
        AuthorizationScopes = []
        #AuthorizationScopes = ["openid", "email", "com.peoplesgroup.ledger.%{if var.environment == "staging" }stg%{ else }${var.environment}%{ endif }/adminUI.all"]
      }
    }
  }

  cfsb_test_proxy_args = { for path, resource in local.cfsb_test_api_mapping :
    path => { for index, value in resource.proxy.methods :
      value => {
        Method = value,
        #AuthorizationType   = "COGNITO_USER_POOLS",
        #AuthorizerId        = aws_api_gateway_authorizer.cfsb_test_cognito.id,
        AuthorizationType = "NONE",
        AuthorizerId      = "",
        IntegrationType   = "HTTP",
        #       URI                 = "http://${aws_lb.api_gw_nlb.dns_name}:80${aws_api_gateway_resource.ledger.path}/${path}/{proxy}",
        URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger.path}/${path}/{proxy}",
        ConnectionType      = "VPC_LINK",
        ConnectionId        = aws_api_gateway_vpc_link.api_gw_vpc_link_nlb_connector_for_alb.id,
        AuthorizationScopes = []
        #AuthorizationScopes = ["openid", "email", "com.peoplesgroup.ledger.%{if var.environment == "staging" }stg%{ else }${var.environment}%{ endif }/adminUI.all"]
      }
    }
  }
}

resource "aws_api_gateway_rest_api" "cfsb_test_api" {
  count                        = var.environment == "qa" ? 1 : 0
  name                         = "${var.project}-cfsb-test-api-${var.environment}"
  description                  = "${title(var.project)} CFSB Test API for ${var.environment}"
  disable_execute_api_endpoint = true
}
resource "aws_api_gateway_base_path_mapping" "cfsb_test" {
  count       = var.environment == "qa" ? 1 : 0
  api_id      = aws_api_gateway_rest_api.cfsb_test_api[0].id
  stage_name  = aws_api_gateway_stage.cfsb_test_stage[0].stage_name
  domain_name = aws_api_gateway_domain_name.domain.domain_name
  base_path   = "cfsb-test"

  # depends_on = [aws_api_gateway_stage.stage]
}
resource "aws_api_gateway_deployment" "cfsb_test_deployment" {
  count       = var.environment == "qa" ? 1 : 0
  rest_api_id = aws_api_gateway_rest_api.cfsb_test_api[0].id

  lifecycle {
    create_before_destroy = true
  }
}
resource "aws_api_gateway_stage" "cfsb_test_stage" {
  count         = var.environment == "qa" ? 1 : 0
  stage_name    = "CFSB-TEST-${upper(var.environment)}"
  deployment_id = aws_api_gateway_deployment.cfsb_test_deployment[0].id
  rest_api_id   = aws_api_gateway_rest_api.cfsb_test_api[0].id

  lifecycle {
    ignore_changes = [deployment_id]
  }
}
resource "aws_api_gateway_method_settings" "cfsb_test_all" {
  count       = var.environment == "qa" ? 1 : 0
  rest_api_id = aws_api_gateway_rest_api.cfsb_test_api[0].id
  stage_name  = aws_api_gateway_stage.cfsb_test_stage[0].stage_name
  method_path = "*/*"

  settings {
    metrics_enabled = true
    logging_level   = "INFO"
  }
}
#resource "aws_api_gateway_authorizer" "cfsb_test_cognito" {
#  name                             = "${var.project}-admin-ui-authorizer-${var.environment}"
#  rest_api_id                      = aws_api_gateway_rest_api.cfsb_test_api.id
#  authorizer_result_ttl_in_seconds = 0
#  type                             = "COGNITO_USER_POOLS"
#  provider_arns                    = [var.cognito_user_pool_arn]
#}
#resource "aws_api_gateway_resource" "cfsb_test" {
#  rest_api_id = aws_api_gateway_rest_api.cfsb_test_api.id
#  parent_id   = aws_api_gateway_rest_api.cfsb_test_api.root_resource_id
#  path_part   = "internal"
#}
resource "aws_api_gateway_resource" "cfsb_test_v1" {
  count       = var.environment == "qa" ? 1 : 0
  rest_api_id = aws_api_gateway_rest_api.cfsb_test_api[0].id
  parent_id   = aws_api_gateway_rest_api.cfsb_test_api[0].root_resource_id
  path_part   = "v1"
}
resource "aws_api_gateway_resource" "cfsb_test_ledger" {
  count       = var.environment == "qa" ? 1 : 0
  rest_api_id = aws_api_gateway_rest_api.cfsb_test_api[0].id
  parent_id   = aws_api_gateway_resource.cfsb_test_v1[0].id
  path_part   = "ledger"
}

module "cfsb_test_api_endpoints" {
  source = "./endpoint"

  for_each = { for key, value in local.cfsb_test_path_args : key => value if var.environment == "qa" }

  rest_api_id        = aws_api_gateway_rest_api.cfsb_test_api[0].id
  parent_resource_id = aws_api_gateway_resource.cfsb_test_ledger[0].id
  resource           = each.key

  endpoint_spec = each.value
  method_request_params = {
    "method.request.header.authorization"              = true
    "method.request.header.x-pg-interaction-id"        = true
    "method.request.header.x-pg-interaction-timestamp" = true
    "method.request.header.x-pg-profile-id"            = each.key == "account" || each.key == "transaction" ? true : false
    "method.request.header.x-pg-account-id"            = each.key == "transaction" ? true : false
  }
  integration_request_params = {
    "integration.request.header.authorization"              = "method.request.header.authorization"
    "integration.request.header.x-pg-interaction-id"        = "method.request.header.x-pg-interaction-id"
    "integration.request.header.x-pg-interaction-timestamp" = "method.request.header.x-pg-interaction-timestamp"
    "integration.request.header.x-pg-profile-id"            = "method.request.header.x-pg-profile-id"
    "integration.request.header.x-pg-account-id"            = "method.request.header.x-pg-account-id"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-account-id'"
  cors_allowed_methods = "'GET,OPTIONS,POST'"
  cors_allowed_origins = "'*'"
}
module "cfsb_test_endpoints_proxy" {
  source = "./endpoint"

  for_each = { for key, value in local.cfsb_test_proxy_args : key => value if var.environment == "qa" }

  rest_api_id        = aws_api_gateway_rest_api.cfsb_test_api[0].id
  parent_resource_id = module.cfsb_test_api_endpoints[each.key].resource_id
  resource           = "{proxy+}"

  endpoint_spec = each.value
  method_request_params = {
    "method.request.path.proxy"                        = true
    "method.request.header.authorization"              = true
    "method.request.header.x-pg-interaction-id"        = true
    "method.request.header.x-pg-interaction-timestamp" = true
    "method.request.header.x-pg-profile-id"            = each.key == "account" || each.key == "transaction" ? true : false
    "method.request.header.x-pg-account-id"            = each.key == "transaction" ? true : false
  }
  integration_request_params = {
    "integration.request.path.proxy"                        = "method.request.path.proxy"
    "integration.request.header.authorization"              = "method.request.header.authorization"
    "integration.request.header.x-pg-interaction-id"        = "method.request.header.x-pg-interaction-id"
    "integration.request.header.x-pg-interaction-timestamp" = "method.request.header.x-pg-interaction-timestamp"
    "integration.request.header.x-pg-profile-id"            = "method.request.header.x-pg-profile-id"
    "integration.request.header.x-pg-account-id"            = "method.request.header.x-pg-account-id"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-account-id'"
  cors_allowed_methods = "'GET,OPTIONS,POST,PATCH,PUT,DELETE'"
  cors_allowed_origins = "'*'"
}
