#locals {
#  api_mapping_no_auth = yamldecode(var.api_res_meth_mappings)
#
#  path_args_no_auth = { for path, resource in local.api_mapping :
#    path => { for index, value in resource.methods :
#      value => {
#        Method            = value,
#        AuthorizationType = "NONE",
#        AuthorizerId      = "",
#        IntegrationType     = "HTTP",
#        URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger_no_auth[0].path}/${path}",
#        ConnectionType      = "VPC_LINK",
#        ConnectionId        = aws_api_gateway_vpc_link.nlb_connector_for_alb.id,
#        AuthorizationScopes = []
#      }
#    } if path == "transaction"
#  }
#
#  proxy_args_no_auth = { for path, resource in local.api_mapping :
#    path => { for index, value in resource.proxy.methods :
#      value => {
#        Method            = value,
#        AuthorizationType = "NONE",
#        AuthorizerId      = "",
#        IntegrationType     = "HTTP",
#        URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger_no_auth[0].path}/${path}/{proxy}",
#        ConnectionType      = "VPC_LINK",
#        ConnectionId        = aws_api_gateway_vpc_link.nlb_connector_for_alb.id,
#        AuthorizationScopes = []
#      }
#    } if path == "transaction"
#  }
#
#  async_args_no_auth = [ for path, resource in local.api_mapping :
#    { for async_key, async_value in resource: 
#      async_key => { for index, value in async_value.methods :
#        value => {
#          Method            = value,
#          AuthorizationType = "AWS_IAM",
#          AuthorizerId      = "",
#          IntegrationType     = "HTTP",
#          URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger_no_auth[0].path}/${path}/async",
#          ConnectionType      = "VPC_LINK",
#          ConnectionId        = aws_api_gateway_vpc_link.nlb_connector_for_alb.id,
#          AuthorizationScopes = []
#        } 
#      } if async_key == "async" 
#    } if path == "transaction"
#  ]
#
#  async_proxy_args_no_auth = { for path, resource in local.api_mapping :
#    path => { for index, value in resource.async.proxy.methods :
#      value => {
#        Method            = value,
#        AuthorizationType = "AWS_IAM",
#        AuthorizerId      = "",
#        IntegrationType     = "HTTP",
#        URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.ledger_no_auth[0].path}/${path}/async/{proxy}",
#        ConnectionType      = "VPC_LINK",
#        ConnectionId        = aws_api_gateway_vpc_link.nlb_connector_for_alb.id,
#        AuthorizationScopes = []
#      }
#    } if path == "transaction"
#  }
#}
#
#resource "aws_api_gateway_base_path_mapping" "this_no_auth" {
#  count       = var.environment == "staging" ? 1 : 0
#  api_id      = aws_api_gateway_rest_api.api_no_auth[0].id
#  stage_name  = aws_api_gateway_stage.stage_no_auth[0].stage_name
#  domain_name = aws_api_gateway_domain_name.domain.domain_name
#  base_path   = "no-auth"
#
#  depends_on = [aws_api_gateway_stage.stage_no_auth[0]]
#}
#resource "aws_api_gateway_rest_api" "api_no_auth" {
#  count       = var.environment == "staging" ? 1 : 0
#  name                         = "${var.project}-pg-system-api-no-auth-${var.environment}"
#  description                  = "${title(var.project)} PG System API (No Auth) for ${var.environment}"
#  disable_execute_api_endpoint = true
#}
#resource "aws_api_gateway_deployment" "deployment_no_auth" {
#  count       = var.environment == "staging" ? 1 : 0
#  rest_api_id = aws_api_gateway_rest_api.api_no_auth[0].id
#
#  lifecycle {
#    create_before_destroy = true
#  }
#}
#resource "aws_api_gateway_stage" "stage_no_auth" {
#  count       = var.environment == "staging" ? 1 : 0
#  stage_name           = "${upper(var.environment)}-NO-AUTH"
#  deployment_id        = aws_api_gateway_deployment.deployment_no_auth[0].id
#  rest_api_id          = aws_api_gateway_rest_api.api_no_auth[0].id
#  xray_tracing_enabled = true
#
#  lifecycle {
#    ignore_changes = [deployment_id]
#  }
#}
#resource "aws_api_gateway_method_settings" "all_no_auth" {
#  count       = var.environment == "staging" ? 1 : 0
#  rest_api_id = aws_api_gateway_rest_api.api_no_auth[0].id
#  stage_name  = aws_api_gateway_stage.stage_no_auth[0].stage_name
#  method_path = "*/*"
#
#  settings {
#    metrics_enabled = true
#    logging_level   = "INFO"
#  }
#}
#resource "aws_api_gateway_resource" "v1_no_auth" {
#  count       = var.environment == "staging" ? 1 : 0
#  rest_api_id = aws_api_gateway_rest_api.api_no_auth[0].id
#  parent_id   = aws_api_gateway_rest_api.api_no_auth[0].root_resource_id
#  path_part   = "v1"
#}
#resource "aws_api_gateway_resource" "ledger_no_auth" {
#  count       = var.environment == "staging" ? 1 : 0
#  rest_api_id = aws_api_gateway_rest_api.api_no_auth[0].id
#  parent_id   = aws_api_gateway_resource.v1_no_auth[0].id
#  path_part   = "ledger"
#}
#
#module "services_api_endpoints_no_auth" {
#  source = "./endpoint"
#
#  for_each = { for key, value in local.path_args_no_auth: key => value if var.environment == "staging" }
#
#  rest_api_id        = aws_api_gateway_rest_api.api_no_auth[0].id
#  parent_resource_id = aws_api_gateway_resource.ledger_no_auth[0].id
#  resource           = each.key
#
#  endpoint_spec = each.value
#  method_request_params = {
#    "method.request.header.x-pg-interaction-id" = true
#    "method.request.header.x-pg-interaction-timestamp" = true
#    "method.request.header.x-pg-profile-id" = each.key == "account" || each.key == "transaction" ? true : false
#    "method.request.header.x-pg-account-id" = each.key == "transaction" ? true : false
#  }
#  integration_request_params = {
#    "integration.request.header.x-pg-interaction-id" = "method.request.header.x-pg-interaction-id"
#    "integration.request.header.x-pg-interaction-timestamp" = "method.request.header.x-pg-interaction-timestamp"
#    "integration.request.header.x-pg-profile-id" = "method.request.header.x-pg-profile-id"
#    "integration.request.header.x-pg-account-id" = "method.request.header.x-pg-account-id"
#  }
#  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-account-id'"
#  cors_allowed_methods = "'GET,OPTIONS,POST'"
#  cors_allowed_origins = "'*'"
#}
#module "endpoints_proxy_no_auth" {
#  source = "./endpoint"
#
#  for_each = { for key, value in local.proxy_args_no_auth: key => value if var.environment == "staging" }
#
#  rest_api_id        = aws_api_gateway_rest_api.api_no_auth[0].id
#  parent_resource_id = module.services_api_endpoints_no_auth[each.key].resource_id
#  resource           = "{proxy+}"
#
#  endpoint_spec = each.value
#  method_request_params = {
#    "method.request.path.proxy" = true
#    "method.request.header.x-pg-interaction-id" = true
#    "method.request.header.x-pg-interaction-timestamp" = true
#    "method.request.header.x-pg-profile-id" = each.key == "account" || each.key == "transaction" ? true : false
#    "method.request.header.x-pg-account-id" = each.key == "transaction" ? true : false
#  }
#  integration_request_params = {
#    "integration.request.path.proxy" = "method.request.path.proxy"
#    "integration.request.header.x-pg-interaction-id" = "method.request.header.x-pg-interaction-id"
#    "integration.request.header.x-pg-interaction-timestamp" = "method.request.header.x-pg-interaction-timestamp"
#    "integration.request.header.x-pg-profile-id" = "method.request.header.x-pg-profile-id"
#    "integration.request.header.x-pg-account-id" = "method.request.header.x-pg-account-id"
#  }
#  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-account-id'"
#  cors_allowed_methods = "'GET,OPTIONS,POST,PATCH,PUT,DELETE'"
#  cors_allowed_origins = "'*'"
#}
#
#module "async_api_endpoints_no_auth" {
#  source = "./endpoint"
#
#  for_each = { for key, value in element(local.async_args_no_auth, 0): key => value if var.environment == "staging" }
#
#  rest_api_id        = aws_api_gateway_rest_api.api_no_auth[0].id
#  parent_resource_id = module.services_api_endpoints_no_auth["transaction"].resource_id
#  resource           = each.key
#
#  endpoint_spec = each.value
#  method_request_params = {
#    "method.request.header.x-pg-interaction-id" = true
#    "method.request.header.x-pg-interaction-timestamp" = true
#    "method.request.header.x-pg-profile-id" = true
#    "method.request.header.x-pg-account-id" = true
#  }
#  integration_request_params = {
#    "integration.request.header.x-pg-interaction-id" = "method.request.header.x-pg-interaction-id"
#    "integration.request.header.x-pg-interaction-timestamp" = "method.request.header.x-pg-interaction-timestamp"
#    "integration.request.header.x-pg-profile-id" = "method.request.header.x-pg-profile-id"
#    "integration.request.header.x-pg-account-id" = "method.request.header.x-pg-account-id"
#  }
#  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-account-id'"
#  cors_allowed_methods = "'GET,OPTIONS,POST'"
#  cors_allowed_origins = "'*'"
#}
#module "async_proxy_no_auth" {
#  source = "./endpoint"
#
#  for_each = { for key, value in local.async_proxy_args_no_auth: key => value if var.environment == "staging" }
#
#  rest_api_id        = aws_api_gateway_rest_api.api_no_auth[0].id
#  parent_resource_id = module.async_api_endpoints_no_auth["async"].resource_id
#  resource           = "{proxy+}"
#
#  endpoint_spec = each.value
#  method_request_params = {
#    "method.request.path.proxy" = true
#    "method.request.header.x-pg-interaction-id" = true
#    "method.request.header.x-pg-interaction-timestamp" = true
#    "method.request.header.x-pg-profile-id" = true
#    "method.request.header.x-pg-account-id" = true
#  }
#  integration_request_params = {
#    "integration.request.path.proxy" = "method.request.path.proxy"
#    "integration.request.header.x-pg-interaction-id" = "method.request.header.x-pg-interaction-id"
#    "integration.request.header.x-pg-interaction-timestamp" = "method.request.header.x-pg-interaction-timestamp"
#    "integration.request.header.x-pg-profile-id" = "method.request.header.x-pg-profile-id"
#    "integration.request.header.x-pg-account-id" = "method.request.header.x-pg-account-id"
#  }
#  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-account-id'"
#  cors_allowed_methods = "'GET,OPTIONS,POST,PATCH,PUT,DELETE'"
#  cors_allowed_origins = "'*'"
#}
#
