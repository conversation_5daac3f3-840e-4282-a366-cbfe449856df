variable "certificate_arn" {
  type        = string
  description = "SSL Certificate ARN from ACM"
}

variable "domain_name" {
  type        = string
  description = "Domain name for etransfer api"
}

variable "aws_account" {
  type = string
}
variable "environment" {
  type        = string
  description = "Environment containing these resources"
}

variable "project" {
  type        = string
  description = "Project owning these resources"
}

variable "alb_hostname" {
  type        = string
  description = "ALB hostname created by Kubernetes ALB Controller"
}

variable "api_res_meth_mappings" {
  type = string
}

variable "admin_ui_meth_mappings" {
  type = string
}

variable "vpc_id" {
  type = string
}

variable "private_subnet_ids" {
  type = list(string)
}

variable "public_subnet_ids" {
  type = list(string)
}

variable "enable_waf" {
  type = bool
}

variable "tags" {
  type = map(string)
}

variable "cognito_user_pool_arn" {}

variable "util_lb_name" {
  type = string
}
variable "create_util_nlb" {
  type    = bool
  default = false
}
variable "create_util_tg" {
  type    = bool
  default = false
}
variable "data_trace" {
  type    = bool
  default = false
}

variable "eks_cluster_name" {
  type        = string
  description = "Name of the EKS cluster"
}

# variable "access_logs_bucket_name" {
#   type = string
# }