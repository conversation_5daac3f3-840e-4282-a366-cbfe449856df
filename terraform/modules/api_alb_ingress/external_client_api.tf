#### For external clients (/v1/ledger/account/{proxy}/balance) ###

resource "aws_api_gateway_rest_api" "external_client_api" {
  name                         = "${var.project}-external-api-${var.environment}"
  description                  = "${title(var.project)} External Client API for ${var.environment}"
  disable_execute_api_endpoint = true
}
resource "aws_api_gateway_base_path_mapping" "external_client" {
  api_id      = aws_api_gateway_rest_api.external_client_api.id
  stage_name  = aws_api_gateway_stage.external_client_stage.stage_name
  domain_name = aws_api_gateway_domain_name.domain.domain_name

  # depends_on = [aws_api_gateway_stage.stage]
}
resource "aws_api_gateway_deployment" "external_client_deployment" {
  rest_api_id = aws_api_gateway_rest_api.external_client_api.id

  lifecycle {
    create_before_destroy = true
  }
}
resource "aws_api_gateway_stage" "external_client_stage" {
  stage_name    = "EXTERNAL-${upper(var.environment)}"
  deployment_id = aws_api_gateway_deployment.external_client_deployment.id
  rest_api_id   = aws_api_gateway_rest_api.external_client_api.id

  lifecycle {
    ignore_changes = [deployment_id]
  }
}
resource "aws_api_gateway_method_settings" "external_client_all" {
  rest_api_id = aws_api_gateway_rest_api.external_client_api.id
  stage_name  = aws_api_gateway_stage.external_client_stage.stage_name
  method_path = "*/*"

  settings {
    metrics_enabled    = true
    logging_level      = "INFO"
    data_trace_enabled = var.data_trace
  }
}
resource "aws_api_gateway_authorizer" "external_client_cognito" {
  name                             = "${var.project}-external-client-authorizer-${var.environment}"
  rest_api_id                      = aws_api_gateway_rest_api.external_client_api.id
  authorizer_result_ttl_in_seconds = 0
  type                             = "COGNITO_USER_POOLS"
  provider_arns                    = [var.cognito_user_pool_arn]
}
resource "aws_api_gateway_resource" "external_v1" {
  rest_api_id = aws_api_gateway_rest_api.external_client_api.id
  parent_id   = aws_api_gateway_rest_api.external_client_api.root_resource_id
  path_part   = "v1"
}
resource "aws_api_gateway_resource" "external_ledger" {
  rest_api_id = aws_api_gateway_rest_api.external_client_api.id
  parent_id   = aws_api_gateway_resource.external_v1.id
  path_part   = "ledger"
}
resource "aws_api_gateway_resource" "external_account" {
  rest_api_id = aws_api_gateway_rest_api.external_client_api.id
  parent_id   = aws_api_gateway_resource.external_ledger.id
  path_part   = "account"
}
module "account_external" {
  source = "./endpoint"

  #  for_each = local.proxy_args

  rest_api_id        = aws_api_gateway_rest_api.external_client_api.id
  parent_resource_id = aws_api_gateway_resource.external_account.id
  resource           = "{proxy+}"

  endpoint_spec = { "GET" = {
    Method              = "GET",
    AuthorizationType   = "COGNITO_USER_POOLS",
    AuthorizerId        = aws_api_gateway_authorizer.external_client_cognito.id,
    IntegrationType     = "HTTP",
    URI                 = "http://${var.domain_name}:80${aws_api_gateway_resource.external_account.path}/{proxy}",
    ConnectionType      = "VPC_LINK",
    ConnectionId        = aws_api_gateway_vpc_link.api_gw_vpc_link_nlb_connector_for_alb.id,
    AuthorizationScopes = ["com.peoplesgroup.ledger.%{if var.environment == "staging"}stg%{else}${var.environment}%{endif}/publicRestAPI.all"]
  } }
  method_request_params = {
    "method.request.path.proxy"                        = true
    "method.request.header.Authorization"              = true
    "method.request.header.x-pg-interaction-id"        = true
    "method.request.header.x-pg-interaction-timestamp" = true
    "method.request.header.x-pg-profile-id"            = true
    "method.request.header.x-pg-account-id"            = false
    "method.request.header.x-pg-amz-request-id"        = true
  }
  integration_request_params = {
    "integration.request.path.proxy"                        = "method.request.path.proxy"
    "integration.request.header.Authorization"              = "method.request.header.Authorization"
    "integration.request.header.x-pg-interaction-id"        = "method.request.header.x-pg-interaction-id"
    "integration.request.header.x-pg-interaction-timestamp" = "method.request.header.x-pg-interaction-timestamp"
    "integration.request.header.x-pg-profile-id"            = "method.request.header.x-pg-profile-id"
    "integration.request.header.x-pg-account-id"            = "method.request.header.x-pg-account-id"
    "integration.request.header.x-pg-amz-request-id"        = "context.requestId"
  }
  cors_allowed_headers = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-interaction-id,x-pg-interaction-timestamp,x-pg-profile-id,x-pg-amz-request-id'"
  cors_allowed_methods = "'GET'"
  cors_allowed_origins = "'*'"
}
