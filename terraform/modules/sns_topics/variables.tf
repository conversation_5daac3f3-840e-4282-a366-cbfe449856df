variable "project" {
  type = string
}
variable "environment" {
  type = string
}
variable "account_id" {
  type = string
}
variable "balance_alert_rcpts" {
  type = list(string)
}
variable "sns_topic_arn" {
}
variable "tags" {
}
variable "eks_cluster_name" {
  type = string
}
variable "kafka_topic" {
  type = string
}
variable "broker_id" {
  type = string
}
variable "pending_topic_v2" {
  type = string
}