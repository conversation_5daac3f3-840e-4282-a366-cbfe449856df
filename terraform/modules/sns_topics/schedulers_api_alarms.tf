locals {
  log_group_name = "/%{if var.environment != "prod"}non-prod%{else}production%{endif}/%{if var.environment == "qa"}dev-qa%{else}${var.environment}%{endif}/general-ledger/eks/${var.project}-eks-main-%{if var.environment == "qa"}devqa%{else}${var.environment}%{endif}/application"
}
resource "aws_cloudwatch_log_metric_filter" "schedulers_api_generic_error" {
  log_group_name = local.log_group_name
  name           = "${var.project}-schedulers-api-generic-error-${var.environment}"
  pattern        = "SchedulersApi Generic error"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "${var.project}-schedulers-api-generic-error-${var.environment}"
    namespace     = "${upper(var.project)}-${upper(var.environment)}"
    unit          = "Count"
    value         = "1"
  }
}
resource "aws_cloudwatch_log_metric_filter" "schedulers_api_db_error" {
  log_group_name = local.log_group_name
  name           = "${var.project}-schedulers-api-db-error-${var.environment}"
  pattern        = "SchedulersApi Database error"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "${var.project}-schedulers-api-db-error-${var.environment}"
    namespace     = "${upper(var.project)}-${upper(var.environment)}"
    unit          = "Count"
    value         = "1"
  }
}
resource "aws_cloudwatch_log_metric_filter" "schedulers_api_null_pointer_error" {
  log_group_name = local.log_group_name
  name           = "${var.project}-schedulers-api-null-pointer-error-${var.environment}"
  pattern        = "SchedulersApi Null Pointer error"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "${var.project}-schedulers-api-null-pointer-error-${var.environment}"
    namespace     = "${upper(var.project)}-${upper(var.environment)}"
    unit          = "Count"
    value         = "1"
  }
}
resource "aws_cloudwatch_metric_alarm" "schedulers_api_generic_error" {
  actions_enabled = true
  alarm_actions = [
    aws_sns_topic.balance_alerts.arn,
  ]
  alarm_name                = "${var.project}-schedulers-api-generic-error-alarm-${var.environment}"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm       = 3
  dimensions                = {}
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name               = aws_cloudwatch_log_metric_filter.schedulers_api_generic_error.id
  namespace                 = "${upper(var.project)}-${upper(var.environment)}"
  ok_actions                = var.sns_topic_arn
  period                    = 60
  statistic                 = "Average"
  tags                      = var.tags
  threshold                 = 1
  treat_missing_data        = "ignore"
}
resource "aws_cloudwatch_metric_alarm" "schedulers_api_db_error_alarm" {
  actions_enabled = true
  alarm_actions = [
    aws_sns_topic.balance_alerts.arn,
  ]
  alarm_name                = "${var.project}-schedule-api-db-error-alarm-${var.environment}"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm       = 3
  dimensions                = {}
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name               = aws_cloudwatch_log_metric_filter.schedulers_api_db_error.id
  namespace                 = "${upper(var.project)}-${upper(var.environment)}"
  ok_actions                = var.sns_topic_arn
  period                    = 60
  statistic                 = "Average"
  tags                      = var.tags
  threshold                 = 1
  treat_missing_data        = "ignore"
}
resource "aws_cloudwatch_metric_alarm" "schedulers_api_null_point_error_alarm" {
  actions_enabled = true
  alarm_actions = [
    aws_sns_topic.balance_alerts.arn,
  ]
  alarm_name                = "${var.project}-schedule-null-point-error-alarm-${var.environment}"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm       = 3
  dimensions                = {}
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name               = aws_cloudwatch_log_metric_filter.schedulers_api_null_pointer_error.id
  namespace                 = "${upper(var.project)}-${upper(var.environment)}"
  ok_actions                = var.sns_topic_arn
  period                    = 60
  statistic                 = "Average"
  tags                      = var.tags
  threshold                 = 1
  treat_missing_data        = "ignore"
}

resource "aws_cloudwatch_log_metric_filter" "redis_time_out_error" {
  log_group_name = local.log_group_name
  name           = "${var.project}-redis-time-out-error-${var.environment}"
  pattern        = "Redis command timed out"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "${var.project}-redis-time-out-error-${var.environment}"
    namespace     = "${upper(var.project)}-${upper(var.environment)}"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "redis_time_out_error" {
  actions_enabled = true
  alarm_actions = [
    aws_sns_topic.balance_alerts.arn,
  ]
  alarm_name                = "${var.project}-redis-time-out-error-alarm-${var.environment}"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm       = 3
  dimensions                = {}
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name               = aws_cloudwatch_log_metric_filter.redis_time_out_error.id
  namespace                 = "${upper(var.project)}-${upper(var.environment)}"
  ok_actions                = var.sns_topic_arn
  period                    = 300
  statistic                 = "Average"
  tags                      = var.tags
  threshold                 = 1
  treat_missing_data        = "ignore"
}

resource "aws_cloudwatch_log_metric_filter" "ttl_not_in_range_error" {
  log_group_name = local.log_group_name
  name           = "${var.project}-ttl-not-in-range-error-${var.environment}"
  pattern        = "not within the TTL range"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "${var.project}-ttl-not-in-range-error-${var.environment}"
    namespace     = "${upper(var.project)}-${upper(var.environment)}"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ttl_not_in_range_error" {
  actions_enabled = true
  alarm_actions = [
    aws_sns_topic.balance_alerts.arn,
  ]
  alarm_name                = "${var.project}-ttl-not-in-range-alarm-${var.environment}"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm       = 3
  dimensions                = {}
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name               = aws_cloudwatch_log_metric_filter.ttl_not_in_range_error.id
  namespace                 = "${upper(var.project)}-${upper(var.environment)}"
  ok_actions                = var.sns_topic_arn
  period                    = 300
  statistic                 = "Average"
  tags                      = var.tags
  threshold                 = 1
  treat_missing_data        = "ignore"
}

resource "aws_cloudwatch_log_metric_filter" "kafka_lag_error" {
  log_group_name = local.log_group_name
  name           = "${var.project}-kafka-lag-error-${var.environment}"
  pattern        = "Kafka commitId"

  metric_transformation {
    default_value = "0"
    dimensions    = {}
    name          = "${var.project}-kafka-lag-error-${var.environment}"
    namespace     = "${upper(var.project)}-${upper(var.environment)}"
    unit          = "Count"
    value         = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "kafka_lag_error" {
  actions_enabled = true
  alarm_actions = [
    aws_sns_topic.balance_alerts.arn,
  ]
  alarm_name                = "${var.project}-kafka-lag-alarm-${var.environment}"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm       = 3
  dimensions                = {}
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name               = aws_cloudwatch_log_metric_filter.kafka_lag_error.id
  namespace                 = "${upper(var.project)}-${upper(var.environment)}"
  ok_actions                = var.sns_topic_arn
  period                    = 300
  statistic                 = "Average"
  tags                      = var.tags
  threshold                 = 1
  treat_missing_data        = "ignore"
}