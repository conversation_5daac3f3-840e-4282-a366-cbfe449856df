locals {
  consumer_group = var.environment == "qa" ? "ASYNCHRONOUS_API_QAS" : "ASYNCHRONOUS_API_${upper(var.environment)}"
  topic          = var.environment == "qa" ? "PENDING_TRANSACTIONS_QAS" : "PENDING_TRANSACTIONS_${upper(var.environment)}"

}
resource "aws_cloudwatch_metric_alarm" "kafka_lag_alarm" {
  actions_enabled     = true
  alarm_actions       = var.sns_topic_arn
  alarm_name          = "Kafka-Lag-info-Alarm-${upper(var.environment)}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = 3
  dimensions = {
    "Cluster Name"   = "${var.project}-kafka-cluster-${var.environment}"
    "Consumer Group" = var.environment == "prod" ? "ASYNCHRONOUS_API" : local.consumer_group
    "Topic"          = var.environment == "prod" ? "PENDING_TRANSACTIONS" : local.topic
  }
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name               = "MaxOffsetLag"
  namespace                 = "AWS/Kafka"
  ok_actions                = var.sns_topic_arn
  period                    = 300
  statistic                 = "Average"
  tags                      = var.tags
  threshold                 = 20
  treat_missing_data        = "ignore"
}



resource "aws_cloudwatch_metric_alarm" "kafka_lag_alarm_warn" {
  actions_enabled     = true
  alarm_actions       = var.sns_topic_arn
  alarm_name          = "Kafka-Lag-warn-Alarm-${upper(var.environment)}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = 3
  dimensions = {
    "Cluster Name"   = "${var.project}-kafka-cluster-${var.environment}"
    "Consumer Group" = var.environment == "prod" ? "ASYNCHRONOUS_API" : local.consumer_group
    "Topic"          = var.environment == "prod" ? "PENDING_TRANSACTIONS" : local.topic
  }
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name               = "MaxOffsetLag"
  namespace                 = "AWS/Kafka"
  ok_actions                = var.sns_topic_arn
  period                    = 300
  statistic                 = "Average"
  tags                      = var.tags
  threshold                 = 50
  treat_missing_data        = "ignore"
}

resource "aws_cloudwatch_metric_alarm" "kafka_lag_alarm_info_pending_topic_v2" {
  count               = var.environment == "prod" ? 1 : 0
  actions_enabled     = true
  alarm_actions       = var.sns_topic_arn
  alarm_name          = "Kafka-Lag-info-Alarm-pending-topic-v2-${upper(var.environment)}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = 3
  dimensions = {
    "Cluster Name"   = "${var.project}-kafka-cluster-${var.environment}"
    "Consumer Group" = var.environment == "prod" ? "ASYNCHRONOUS_API" : local.consumer_group
    "Topic"          = var.pending_topic_v2
  }
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name              = "MaxOffsetLag"
  namespace                = "AWS/Kafka"
  ok_actions               = var.sns_topic_arn
  period                   = 300
  statistic                = "Average"
  tags                     = var.tags
  threshold                = 20
  treat_missing_data       = "ignore"
}

resource "aws_cloudwatch_metric_alarm" "kafka_lag_alarm_warn_pending_topic_v2" {
  actions_enabled     = true
  alarm_actions       = var.sns_topic_arn
  alarm_name          = "Kafka-Lag-warn-Alarm-pending-topic-v2-${upper(var.environment)}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = 3
  dimensions = {
    "Cluster Name"   = "${var.project}-kafka-cluster-${var.environment}"
    "Consumer Group" = var.environment == "prod" ? "ASYNCHRONOUS_API" : local.consumer_group
    "Topic"          = var.pending_topic_v2
  }
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name              = "MaxOffsetLag"
  namespace                = "AWS/Kafka"
  ok_actions               = var.sns_topic_arn
  period                   = 300
  statistic                = "Average"
  tags                     = var.tags
  threshold                = 50
  treat_missing_data       = "ignore"
}


resource "aws_cloudwatch_metric_alarm" "kafka_manual-intervention-transactions-topic-alarm" {
  actions_enabled     = true
  alarm_actions       = var.sns_topic_arn
  ok_actions          = var.sns_topic_arn
  alarm_name          = "${var.project}-kafka-manual-intervention-transactions-topic-alarm-${var.environment}"
  alarm_description   = "Alarm to monitor the Kafka topic ${var.kafka_topic} for manual intervention"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 1
  datapoints_to_alarm = 3
  dimensions = {
    "Cluster Name"   = "${var.project}-kafka-cluster-${var.environment}"
    "Topic"          = var.kafka_topic
    "Broker ID"      = "1"
  }
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name               = "BytesInPerSec"
  unit                      = "Bytes/Second"
  namespace                 = "AWS/Kafka"
  period                    = 300
  statistic                 = "Average"
  treat_missing_data        = "missing"
  tags                      = var.tags
}

resource "aws_cloudwatch_metric_alarm" "kafka_manual-intervention-transactions-topic-broker-1-alarm-qa" {
  count               = var.environment == "qa" ? 1 : 0
  actions_enabled     = true
  alarm_actions       = var.sns_topic_arn
  ok_actions          = var.sns_topic_arn
  alarm_name          = "${var.project}-kafka-manual-intervention-transactions-topic-broker_id1-alarm-${var.environment}"
  alarm_description   = "Alarm to monitor the Kafka topic ${var.kafka_topic} for manual intervention"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 1
  datapoints_to_alarm = 3
  dimensions = {
    "Cluster Name"   = "${var.project}-kafka-cluster-${var.environment}"
    "Topic"          = var.kafka_topic
    "Broker ID"      = var.broker_id
  }
  evaluation_periods        = 3
  insufficient_data_actions = []
  metric_name               = "BytesInPerSec"
  namespace                 = "AWS/Kafka"
  unit                      = "Bytes/Second"
  period                    = 300
  statistic                 = "Average"
  treat_missing_data        = "missing"
  tags                      = var.tags
}