resource "aws_sns_topic" "balance_alerts" {
  name            = "${var.project}-balance-alerts-${var.environment}"
  delivery_policy = <<EOF
{
  "http": {
    "defaultHealthyRetryPolicy": {
      "minDelayTarget": 20,
      "maxDelayTarget": 20,
      "numRetries": 3,
      "numMaxDelayRetries": 0,
      "numNoDelayRetries": 0,
      "numMinDelayRetries": 0,
      "backoffFunction": "linear"
    },
    "disableSubscriptionOverrides": false,
    "defaultRequestPolicy": {
      "headerContentType": "text/plain; charset=UTF-8"
    }
  }
}
EOF
}

resource "aws_sns_topic_policy" "balance_alerts" {
  arn = aws_sns_topic.balance_alerts.arn

  policy = data.aws_iam_policy_document.sns_topic_policy.json
}

data "aws_iam_policy_document" "sns_topic_policy" {
  policy_id = "__default_policy_ID"

  statement {
    actions = [
      "SNS:Subscribe",
      "SNS:SetTopicAttributes",
      "SNS:RemovePermission",
      "SNS:Receive",
      "SNS:Publish",
      "SNS:ListSubscriptionsByTopic",
      "SNS:GetTopicAttributes",
      "SNS:DeleteTopic",
      "SNS:AddPermission",
    ]

    condition {
      test     = "StringEquals"
      variable = "AWS:SourceOwner"

      values = [
        var.account_id,
      ]
    }

    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    resources = [
      aws_sns_topic.balance_alerts.arn,
    ]

    sid = "__default_statement_ID"
  }
}

resource "aws_sns_topic_subscription" "balance_alerts" {
  for_each               = toset(var.balance_alert_rcpts)
  endpoint               = each.key
  protocol               = var.environment == "prod" ? "https" : "email"
  endpoint_auto_confirms = var.environment == "prod" ? true : false
  raw_message_delivery   = false
  topic_arn              = aws_sns_topic.balance_alerts.arn
}

locals {
  email_rcpts = ["<EMAIL>"]
}

resource "aws_sns_topic_subscription" "balance_alerts_email" {
  for_each               = toset(local.email_rcpts)
  endpoint               = each.key
  protocol               = "email"
  endpoint_auto_confirms = false
  raw_message_delivery   = false
  topic_arn              = aws_sns_topic.balance_alerts.arn
}
