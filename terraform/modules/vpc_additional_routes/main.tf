locals {
  route_table_count = length(var.route_table_ids)
  cidr_block_count  = length(var.cidr_blocks)
}

resource "aws_route" "additional_routes" {
  count                  = local.route_table_count * local.cidr_block_count
  route_table_id         = element(var.route_table_ids, ceil(count.index / local.cidr_block_count))
  destination_cidr_block = element(var.cidr_blocks, count.index % local.cidr_block_count)
  transit_gateway_id     = var.tgw_id
}

