resource "kubernetes_namespace" "namespace" {
    count = var.create_namespace ? 1 : 0
    metadata {
        name = "pg-${var.project}-${var.environment}"
    }
}

resource "kubernetes_namespace" "mgmt_namespace" {
    count = var.create_management_namespace ? 1 : 0
    metadata {
        name = "pg-${var.project}-mgmt"
    }
}

resource "kubernetes_service" "services" {
    for_each = toset(var.service_names)
    metadata {
        name = "${each.value}-service"
        namespace = kubernetes_namespace.namespace.0.metadata.0.name
        annotations  = {
            "service.beta.kubernetes.io/aws-load-balancer-internal" = "false"
            "service.beta.kubernetes.io/aws-load-balancer-type" = "nlb"
        }
    }
    spec {
        selector = {
        app = "${each.value}-v1"
        }
        port {
        port        = 8080
        target_port = 8080
        }

        type = "LoadBalancer"
    }
}
