output "loadbalancers_dns" {
    value = [
        for hostname in kubernetes_service.services : hostname.status.0.load_balancer.0.ingress.0.hostname
    ]
    # value = kubernetes_service.services.*.status.0.load_balancer.0.ingress.0.hostname
}

output "loadbalancers_name" {
    value = [
        for hostname in kubernetes_service.services : split("-", hostname.status.0.load_balancer.0.ingress.0.hostname)[0]
    ]
}

output "loadbalancers" {
    # value = [
    #     for service in kubernetes_service.services : service
    # ]
    value = kubernetes_service.services

}
