data "aws_autoscaling_group" "asg" {
  name = var.asg_name
}

resource "aws_autoscaling_schedule" "scale_in" {
  scheduled_action_name  = "scale-out"
  min_size               = 0
  max_size               = 1
  desired_capacity       = 0
  time_zone              = "America/Toronto"
  recurrence             = "00 19 * * MON-FRI"
  autoscaling_group_name = data.aws_autoscaling_group.asg.name
}

resource "aws_autoscaling_schedule" "scale_out" {
  scheduled_action_name  = "scale-in"
  min_size               = var.asg_min_size
  max_size               = var.asg_max_size
  desired_capacity       = var.asg_desired_capacity
  time_zone              = "America/Toronto"
  recurrence             = "00 07 * * MON-FRI"
  autoscaling_group_name = data.aws_autoscaling_group.asg.name
}