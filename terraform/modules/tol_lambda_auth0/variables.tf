variable "name" {
  description = "The name of the Lambda function"
  type        = string
}

variable "environment" {
  description = "The environment for the Lambda function"
  type        = string
}

variable "tags" {
  description = "Tags to apply to the Lambda function"
  type        = map(string)
}

variable "s3_bucket" {
  description = "The S3 bucket for the Lambda function"
  type        = string
}

variable "aws_account_number" {
  description = "The AWS account number for the Lambda function"
  type        = string
}

variable "region" {
  description = "The AWS region for the Lambda function"
  type        = string
}

variable "s3_key" {
  description = "The S3 key for the Lambda function code"
  type        = string
  default     = "lambda_package_bedrock.zip"
}