# CloudFormation template for Lambda with nodejs22.x support
resource "aws_cloudformation_stack" "lambda_auth0" {
  name = var.name

  template_body = jsonencode({
    AWSTemplateFormatVersion = "2010-09-09"
    Description              = "Lambda function for Auth0 API Gateway integration on ${upper(var.environment)} environment"

    Resources = {
      LambdaFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName = var.name
          Runtime      = "nodejs22.x"
          Handler      = "index.handler"
          Code = {
            S3Bucket = var.s3_bucket
            S3Key    = var.s3_key
          }
          Role        = aws_iam_role.lambda_execution_role.arn
          Description = "Lambda function for Auth0 API Gateway integration on ${upper(var.environment)} environment"
          MemorySize  = 128
          Timeout     = 300
          EphemeralStorage = {
            Size = 512
          }
          PackageType = "Zip"
          Tags = [
            for key, value in var.tags : {
              Key   = key
              Value = value
            }
          ]
        }
      }

      LambdaPermission = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName  = { Ref = "LambdaFunction" }
          Action        = "lambda:InvokeFunction"
          Principal     = "apigateway.amazonaws.com"
          SourceArn     = "arn:aws:execute-api:${var.region}:${var.aws_account_number}:ljy51gjcn2/authorizers/yiou7h"
          SourceAccount = var.aws_account_number
        }
      }
    }

    Outputs = {
      LambdaFunctionArn = {
        Description = "ARN of the Lambda function"
        Value       = { "Fn::GetAtt" = ["LambdaFunction", "Arn"] }
      }
      LambdaFunctionName = {
        Description = "Name of the Lambda function"
        Value       = { Ref = "LambdaFunction" }
      }
    }
  })

  capabilities = ["CAPABILITY_IAM"]

  tags = var.tags
}

resource "aws_iam_role" "lambda_execution_role" {
  name = "tol-lambda-eks-auth0-${var.environment}-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}