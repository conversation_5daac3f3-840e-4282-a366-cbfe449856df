######################
# SSM Session Manager
######################

data "aws_iam_policy_document" "ec2_assume_policy" {
  statement {
    actions = [
      "sts:AssumeRole",
    ]

    principals {
      type = "Service"

      identifiers = [
        "ec2.amazonaws.com",
      ]
    }
  }
}

resource "aws_iam_role_policy" "this" {
  for_each = toset(var.additional_iam_policies)
  policy   = each.value
  role     = aws_iam_role.ssm_session_manager.id
}

resource "aws_iam_role_policy_attachment" "ssm_session_manager" {
  role       = aws_iam_role.ssm_session_manager.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}


resource "aws_iam_role" "ssm_session_manager" {
  name               = "${var.project}-${var.name}-session-manager-role-${var.environment}"
  assume_role_policy = data.aws_iam_policy_document.ec2_assume_policy.json
  tags               = var.tags
}

resource "aws_iam_instance_profile" "ec2_ssm" {
  name = "${var.project}-${var.name}-session-manager-instance-profile-${var.environment}"
  role = aws_iam_role.ssm_session_manager.name
}

resource "aws_key_pair" "key" {
  key_name   = "${var.project}-key-pair-${var.environment}"
  public_key = var.public_key
}

module "bastion_instance" {
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "~> 3.0"
  count   = var.environment == "qa" ? 1 : 0
  name = format("%s-%s-bastion-%s", var.project, var.name, var.environment)

  ami                         = var.ami_id
  instance_type               = var.instance_type
  monitoring                  = true
  disable_api_termination     = true
  vpc_security_group_ids      = var.bastion_sg_id
  subnet_id                   = var.bastion_subnet_id
  associate_public_ip_address = false
  iam_instance_profile        = aws_iam_instance_profile.ec2_ssm.id
  key_name                    = aws_key_pair.key.key_name
  root_block_device = [
    {
      volume_type = "gp3"
      volume_size = 150
    },
  ]

  tags = merge(var.tags, { DatadogAllowed = false })
}