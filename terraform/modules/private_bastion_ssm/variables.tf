variable "project" {
  type = string
}

variable "name" {
  type = string
}

variable "ami_id" {
  type = string
}

variable "instance_type" {
  type = string
}

variable "bastion_sg_id" {
  type = list(string)
}

variable "vpc_id" {
  type = string
}

variable "vpc_region" {
  type = string
}

variable "environment" {
  type = string
}

variable "bastion_subnet_id" {
  type = string
}

variable "public_key" {
  type = string
}

variable "additional_iam_policies" {
  type = list(string)
}

variable "tags" {
  type = map(string)
}