resource "aws_backup_plan" "this" {
  count = var.enable ? 1 : 0
  name  = "${var.project}-backup-plan-${var.environment}"

  rule {
    rule_name           = "${var.project}-daily-backup-rule-${var.environment}"
    target_vault_name   = aws_backup_vault.this[count.index].name
    schedule            = "${var.backup_schedule}"
    recovery_point_tags = var.tags

    lifecycle {
      delete_after = var.delete_after
    }
  }

  tags = var.tags
}

resource "aws_backup_vault" "this" {
  count = var.enable ? 1 : 0
  name  = "${var.project}-backup-vault-${var.environment}"
  tags  = var.tags
}

resource "aws_backup_selection" "this" {
  count        = var.enable ? 1 : 0
  iam_role_arn = var.aws_backup_role
  name         = "${var.project}-backup-selection-${var.environment}"
  plan_id      = aws_backup_plan.this[count.index].id

  selection_tag {
    type  = "STRINGEQUALS"
    key   = "Project"
    value = "${var.project}"
  }

  selection_tag {
    type  = "STRINGEQUALS"
    key   = "Environment"
    value = "${var.environment}"
  }

  resources = var.resources_arns
}

resource "aws_backup_selection" "eks_nodes" {
  count        = var.enable ? 1 : 0
  iam_role_arn = var.aws_backup_role
  name         = "${var.project}-backup-selection-${var.environment}"
  plan_id      = aws_backup_plan.this[count.index].id

  selection_tag {
    type  = "STRINGEQUALS"
    key   = "aws:eks:cluster-name"
    value = "gl-eks-main-${var.environment}"
  }
}
