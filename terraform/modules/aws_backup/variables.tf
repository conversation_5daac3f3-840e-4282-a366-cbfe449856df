variable "aws_backup_role" {
  description = "IAM role assumed by the aws backup service to perform backup/restore"
}

variable "backup_schedule" {
  description = "CRON syntax script specifying backup schedule"
}

variable "delete_after" {
  description = "Value in days after creation when the recovery point is deleted"
}

variable "enable" {
  type        = bool
  description = "Whether or not to create resources provided by this module"
}

variable "environment" {
  description = "Environment where this resources are deployed"
}

variable "project" {
  description = "Project that owns this resources"
}

variable "resources_arns" {
  type        = list
  description = "ARN of resources that are not backed-up by tags"
  default     = []
}

variable "tags" {
  type        = map
  description = "Tags for resources contained in this module"
}
