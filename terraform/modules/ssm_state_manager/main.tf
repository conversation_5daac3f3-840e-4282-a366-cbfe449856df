data "aws_rds_cluster" "cluster" {
  cluster_identifier = var.db_cluster_identifier
}

# resource "aws_ssm_association" "stop_rds_mondays" {
#   name = "AWS-StartStopAuroraCluster"
#   association_name = "Stop-RDS-Cluster-Mondays"
#   # Cron expression example
#   schedule_expression = "cron(0 23 ? * MON *)"
#   apply_only_at_cron_interval = true

#   parameters = {
#     ClusterName = data.aws_rds_cluster.cluster.id
#     Action = "Stop"
#     AutomationAssumeRole = var.rds_automation_role_arn
#   }
# }

# resource "aws_ssm_association" "start_rds_mondays" {
#   name = "AWS-StartStopAuroraCluster"
#   association_name = "Start-RDS-Cluster-Mondays"
#   apply_only_at_cron_interval = true

#   schedule_expression = "cron(00 11 ? * MON *)"

#   parameters = {
#     ClusterName = data.aws_rds_cluster.cluster.id
#     Action = "Start"
#     AutomationAssumeRole = var.rds_automation_role_arn
#   }
# }

# resource "aws_ssm_association" "stop_rds_tuesdays" {
#   name = "AWS-StartStopAuroraCluster"
#   association_name = "Stop-RDS-Cluster-Tuesdays"
#   apply_only_at_cron_interval = true

#   schedule_expression = "cron(0 23 ? * TUE *)"

#   parameters = {
#     ClusterName = data.aws_rds_cluster.cluster.id
#     Action = "Stop"
#     AutomationAssumeRole = var.rds_automation_role_arn
#   }
# }

# resource "aws_ssm_association" "start_rds_tuesdays" {
#   name = "AWS-StartStopAuroraCluster"
#   association_name = "Start-RDS-Cluster-Tuesdays"
#   apply_only_at_cron_interval = true

#   schedule_expression = "cron(00 11 ? * TUE *)"

#   parameters = {
#     ClusterName = data.aws_rds_cluster.cluster.id
#     Action = "Start"
#     AutomationAssumeRole = var.rds_automation_role_arn
#   }
# }

# resource "aws_ssm_association" "stop_rds_wednesdays" {
#   name = "AWS-StartStopAuroraCluster"
#   association_name = "Stop-RDS-Cluster-Wednesdays"
#   apply_only_at_cron_interval = true
#   schedule_expression = "cron(0 23 ? * WED *)"

#   parameters = {
#     ClusterName = data.aws_rds_cluster.cluster.id
#     Action = "Stop"
#     AutomationAssumeRole = var.rds_automation_role_arn
#   }
# }

# resource "aws_ssm_association" "start_rds_wednesdays" {
#   name = "AWS-StartStopAuroraCluster"
#   association_name = "Start-RDS-Cluster-Wednesdays"

#   schedule_expression = "cron(00 11 ? * WED *)"
#   apply_only_at_cron_interval = true

#   parameters = {
#     ClusterName = data.aws_rds_cluster.cluster.id
#     Action = "Start"
#     AutomationAssumeRole = var.rds_automation_role_arn
#   }
# }

# resource "aws_ssm_association" "stop_rds_thursdays" {
#   name = "AWS-StartStopAuroraCluster"
#   association_name = "Stop-RDS-Cluster-Thursdays"
#   schedule_expression = "cron(0 23 ? * THU *)"
#   apply_only_at_cron_interval = true

#   parameters = {
#     ClusterName = data.aws_rds_cluster.cluster.id
#     Action = "Stop"
#     AutomationAssumeRole = var.rds_automation_role_arn
#   }
# }

# resource "aws_ssm_association" "start_rds_thursdays" {
#   name = "AWS-StartStopAuroraCluster"
#   association_name = "Start-RDS-Cluster-Thursdays"

#   schedule_expression = "cron(00 11 ? * THU *)"
#   apply_only_at_cron_interval = true

#   parameters = {
#     ClusterName = data.aws_rds_cluster.cluster.id
#     Action = "Start"
#     AutomationAssumeRole = var.rds_automation_role_arn
#   }
# }

# resource "aws_ssm_association" "stop_rds_fridays" {
#   name = "AWS-StartStopAuroraCluster"
#   association_name = "Stop-RDS-Cluster-Fridays"

#   schedule_expression = "cron(0 23 ? * FRI *)"
#   apply_only_at_cron_interval = true

#   parameters = {
#     ClusterName = data.aws_rds_cluster.cluster.id
#     Action = "Stop"
#     AutomationAssumeRole = var.rds_automation_role_arn
#   }
# }

# resource "aws_ssm_association" "start_rds_fridays" {
#   name = "AWS-StartStopAuroraCluster"
#   association_name = "Start-RDS-Cluster-Fridays"

#   schedule_expression = "cron(00 11 ? * FRI *)"
#   apply_only_at_cron_interval = true

#   parameters = {
#     ClusterName = data.aws_rds_cluster.cluster.id
#     Action = "Start"
#     AutomationAssumeRole = var.rds_automation_role_arn
#   }
# }


resource "aws_ssm_association" "start_rds" {
  name = "AWS-StartStopAuroraCluster"
  association_name = "Start-RDS-Cluster"


  parameters = {
    ClusterName = data.aws_rds_cluster.cluster.id
    Action = "Start"
    AutomationAssumeRole = var.rds_automation_role_arn
  }
}

resource "aws_ssm_association" "stop_rds" {
  name = "AWS-StartStopAuroraCluster"
  association_name = "Stop-RDS-Cluster"

  parameters = {
    ClusterName = data.aws_rds_cluster.cluster.id
    Action = "Stop"
    AutomationAssumeRole = var.rds_automation_role_arn
  }
}