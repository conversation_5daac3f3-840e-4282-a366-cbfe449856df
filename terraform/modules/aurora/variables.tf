variable "availability_zones" {
  description = "A list of availability zones for the Aurora cluster."
  type        = list
}

variable "backup_retention_period" {
  description = "The number of days to retain backups for."
}
variable "backup_window" {
  description = "The daily time range during which automated backups are created if automated backups are enabled."
}
variable "create_db_cluster" {
  description = "Whether to create the Aurora DB cluster."
}

variable "create_db_cluster_parameter_group" {
  description = "Whether to create a database cluster parameter group"
  default     = true
}

variable "cluster_identifier" {
  description = "The identifier for the Aurora DB cluster."
}

variable "cluster_instance_count" {
  default     = 1
  description = "Number of instances running in the Aurora cluster"
}

variable "database_name" {
  description = "The name of the database to create when the DB cluster is created."
}
variable "db_subnet_group_name" {
  description = "The name of the DB subnet group to use for the DB cluster."
}
variable "engine" {
  description = "The name of the database engine to be used for this DB cluster."
}
variable "engine_version" {
  description = "The version number of the database engine to use."
}
variable "family" {
  description = "The family of the DB cluster parameter group."
}
variable "final_snapshot_identifier" {
  description = "The identifier of the final snapshot created when the DB cluster is deleted."
}
variable "instance_class" {
  description = "The instance class to use for the DB instances in the cluster."
}
variable "maintenance_window" {
  description = "The weekly time range during which system maintenance can occur, in Universal Coordinated Time (UTC)."
}
variable "master_username" {
  description = "The username for the master database user."
}
variable "master_password" {
  description = "The password for the master database user."
}

variable "parameters" {
  description = "A list of DB parameter maps to apply"
  default     = []
}

variable "port" {
  description = "The port on which the DB instances in the DB cluster accept connections."
}
variable "storage_encrypted" {
  description = "Specifies whether the DB cluster is encrypted."
  default     = true
}

variable "encryption_kms_key_id" {
  description = "The ARN for the KMS key to be used for encrypting the DB cluster."
}

variable "vpc_security_group_ids" {
  description = "A list of VPC security groups to associate with the DB cluster."
  type        = list
}

variable "apply_immediately" {
  description = "Specifies whether any cluster modifications are applied immediately, or during the next maintenance window."
  default     = false
}

variable "tags" {
  description = "A map of tags to assign to the DB cluster."
  type        = map
}

variable "db_deletion_protection" {
  type        = bool
  description = "deletion protection for db"
}

variable "snapshot_identifier" {
  description = "Specifies the snapshot identifier from which to restore the DB cluster."
  type        = string
  default     = null
}

variable "auto_minor_version_upgrade" {
  description = "Indicates that minor engine upgrades will be applied automatically to the DB instance during the maintenance window."
  type        = bool
  default     = true
}