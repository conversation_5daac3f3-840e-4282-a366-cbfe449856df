##############################
# DB Cluster parameter group #
##############################
resource "aws_rds_cluster_parameter_group" "this" {
  # count = var.create ? 1 : 0

  name_prefix = var.name_prefix
  description = "Database cluster parameter group for ${var.identifier}"
  family      = var.family

  dynamic "parameter" {
    for_each = var.parameters
    content {
      name         = parameter.value.name
      value        = parameter.value.value
      apply_method = parameter.value.apply_method
    }
  }

  tags = merge(var.tags, map("Name", format("%s", var.identifier)))

  lifecycle {
    create_before_destroy = true
  }
}
