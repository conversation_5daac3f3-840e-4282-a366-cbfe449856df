resource "aws_rds_cluster_instance" "cluster_instances" {
  count                      = var.create_db_cluster ? var.cluster_instance_count : 0
  identifier                 = "${var.cluster_identifier}-instance-${count.index}"
  cluster_identifier         = aws_rds_cluster.default[0].id
  instance_class             = var.instance_class
  engine                     = var.engine
  engine_version             = var.engine_version
  auto_minor_version_upgrade = var.auto_minor_version_upgrade
  availability_zone          = element(var.availability_zones, count.index)
  # monitoring_interval = 60
}

resource "aws_rds_cluster" "default" {
  count                        = var.create_db_cluster ? 1 : 0
  cluster_identifier           = var.cluster_identifier
  engine                       = var.engine
  engine_version               = var.engine_version
  database_name                = var.database_name
  master_username              = var.master_username
  master_password              = var.master_password
  backup_retention_period      = var.backup_retention_period
  preferred_backup_window      = var.backup_window
  preferred_maintenance_window = var.maintenance_window
  port                         = var.port
  vpc_security_group_ids       = var.vpc_security_group_ids
  db_subnet_group_name         = var.db_subnet_group_name
  snapshot_identifier          = var.snapshot_identifier
  # db_cluster_parameter_group_name = var.db_cluster_parameter_group_name
  db_cluster_parameter_group_name = aws_rds_cluster_parameter_group.this.id
  storage_encrypted               = var.storage_encrypted
  final_snapshot_identifier       = var.final_snapshot_identifier
  apply_immediately               = var.apply_immediately
  kms_key_id                      = var.storage_encrypted ? var.encryption_kms_key_id : ""
  deletion_protection             = var.db_deletion_protection

  # serverlessv2_scaling_configuration {
  #   max_capacity = 128
  #   min_capacity = 32
  # }

}

##############################
# DB Cluster parameter group #
##############################
resource "aws_rds_cluster_parameter_group" "this" {
  # count = var.create ? 1 : 0

  name_prefix = var.cluster_identifier
  description = "Database cluster parameter group for ${var.cluster_identifier}"
  family      = var.family
  # family      = "aurora-postgresql12"

  dynamic "parameter" {
    for_each = var.parameters
    content {
      name         = parameter.value.name
      value        = parameter.value.value
      apply_method = parameter.value.apply_method
    }
  }


  lifecycle {
    create_before_destroy = true
  }
}
