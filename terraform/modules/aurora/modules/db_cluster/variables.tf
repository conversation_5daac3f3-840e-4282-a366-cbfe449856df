variable "availability_zones" {
  description = "A list of EC2 Availability Zones for the DB cluster storage."
  type        = list(any)
}

variable "backup_retention_period" {
  description = "The number of days to retain backups for."
}
variable "backup_window" {
  description = "The daily time range during which automated backups are created if automated backups are enabled."
}
variable "cluster_identifier" {
  description = "The cluster identifier."
}

variable "cluster_instance_count" {
  description = "Number of instances running in the Aurora cluster"
}

variable "create_db_cluster" {
  description = "Whether to create the DB cluster."
}
variable "database_name" {
  description = "The name for the database on cluster creation."
}
variable "db_subnet_group_name" {
  description = "The name of the DB subnet group to associate with this DB instance."
}
variable "db_cluster_parameter_group_name" {
  description = "The name of the DB cluster parameter group to associate with this DB cluster."
}
variable "snapshot_identifier" {
  description = "Specifies the snapshot identifier from which to restore the DB cluster."
  type        = string
  default     = null
}
variable "auto_minor_version_upgrade" {
  description = "Indicates that minor engine upgrades will be applied automatically to the DB instance during the maintenance window."
  type        = bool
  default     = true
}
variable "engine" {
  description = "The name of the database engine to be used for this DB cluster."
}

variable "encryption_kms_key_id" {
  description = "The KMS key identifier for an encrypted DB cluster."
}

variable "engine_version" {
  description = "The version number of the database engine to use."
}
variable "final_snapshot_identifier" {
  description = "The identifier of the final snapshot created when the DB cluster is deleted."
}
variable "instance_class" {
  description = "The instance class to use for the DB instances in the cluster."
}
variable "maintenance_window" {
  description = "The weekly time range during which system maintenance can occur, in Universal Coordinated Time (UTC)."
}
variable "master_username" {
  description = "The master username for the DB cluster."
}
variable "master_password" {
  description = "The master password for the DB cluster."
}
variable "port" {
  description = "The port on which the DB instances in the DB cluster accept connections."
}
variable "family" {
  description = "The family of the DB cluster parameter group."
}

variable "vpc_security_group_ids" {
  description = "A list of VPC security groups to associate with the DB cluster."
  type        = list(any)
}

variable "storage_encrypted" {
  description = "Specifies whether the DB cluster is encrypted."
}

variable "apply_immediately" {
  description = "Specifies whether any cluster modifications are applied immediately, or during the next maintenance window."
  default     = false
}

variable "tags" {
  type    = map(any)
  default = {}
}

variable "parameters" {
  description = "A list of DB cluster parameters to apply."
}

variable "db_deletion_protection" {
  type        = bool
  description = "deletion protection for db"
}
