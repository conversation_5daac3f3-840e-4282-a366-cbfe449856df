#########################
# Amazon Aurora cluster #
#########################

module "db_cluster" {
  source                     = "./modules/db_cluster"
  create_db_cluster          = var.create_db_cluster
  cluster_identifier         = var.cluster_identifier
  cluster_instance_count     = var.cluster_instance_count
  engine                     = var.engine
  engine_version             = var.engine_version
  instance_class             = var.instance_class
  auto_minor_version_upgrade = var.auto_minor_version_upgrade
  availability_zones         = var.availability_zones
  database_name              = var.database_name
  master_username            = var.master_username
  master_password            = var.master_password
  apply_immediately          = var.apply_immediately
  backup_retention_period    = var.backup_retention_period
  backup_window              = var.backup_window
  maintenance_window         = var.maintenance_window
  port                       = var.port
  vpc_security_group_ids     = var.vpc_security_group_ids
  db_subnet_group_name       = var.db_subnet_group_name
  snapshot_identifier        = var.snapshot_identifier
  # db_cluster_parameter_group_name = module.db_cluster_parameter_group.db_parameter_group_id
  db_cluster_parameter_group_name = ""
  family                          = var.family
  storage_encrypted               = var.storage_encrypted
  encryption_kms_key_id           = var.encryption_kms_key_id
  final_snapshot_identifier       = var.final_snapshot_identifier
  db_deletion_protection          = var.db_deletion_protection
  tags                            = var.tags
  parameters = [
    {
      name         = "rds.force_ssl"
      value        = "1"
      apply_method = "pending-reboot"
    },
    {
      name         = "timezone"
      value        = "us/eastern"
      apply_method = "pending-reboot"
    },
    {
      name         = "wal_sender_timeout"
      value        = "0"
      apply_method = "immediate"
    },
    {
      name         = "rds.logical_replication"
      value        = "1"
      apply_method = "pending-reboot"
    },
    {
      name         = "shared_preload_libraries"
      value        = "pg_stat_statements,pg_cron"
      apply_method = "pending-reboot"
    },
    {
      apply_method = "immediate"
      name         = "log_hostname"
      value        = "1"
    }
  ]
}

