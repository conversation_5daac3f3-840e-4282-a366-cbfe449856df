data "aws_iam_policy_document" "codepipeline-access" {
statement {
  sid = "Enable IAM User Permissions"

  effect = "Allow"

  principals {
    type = "AWS"
    identifiers = ["arn:aws:iam::${var.account_number}:root"]
  }
  actions = ["kms:*"]

  resources = ["*"]
}
statement {
  sid = "Enable CodePipeline Permissions"

  effect = "Allow"

  principals {
    type = "AWS"
    identifiers = ["arn:aws:iam::${var.account_number}:role/${var.project}-codepipeline-role-qa", "arn:aws:iam::${var.account_number}:role/codebuild-${var.project}-build-project-main-service-role"]
  }

  actions = ["kms:*"]

  resources = ["arn:aws:s3:::${var.project}-codepipeline-qa/*","arn:aws:s3:::${var.project}-codepipeline-qa/"]
}
}

#resource "aws_iam_policy" "codepipeline-access" {
#  name        = "${var.project}-${var.environment}-codepipeline-kms-access"
#  description = "Allow Codepipeline to access KMS"
#  policy      = data.aws_iam_policy_document.codepipeline-access.json
#}
