locals {
  cmk_description_template    = "%s Customer Managed Key for %s in %s environment"
  cmk_deletion_window_in_days = 30
  cmk_enable_key_rotation     = true
}

resource "aws_kms_key" "cmk_rds" {
  description             = format(local.cmk_description_template, "RDS", var.project, var.environment)
  deletion_window_in_days = local.cmk_deletion_window_in_days
  enable_key_rotation     = local.cmk_enable_key_rotation
  tags                    = merge(var.tags, tomap({"Name" = format("%s-cmk-rds-%s", var.project, var.environment)}))
}

resource "aws_kms_alias" "cmk_rds_alias" {
  name          = format("alias/%s-cmk-rds-%s", var.project, var.environment)
  target_key_id = aws_kms_key.cmk_rds.id
}

resource "aws_kms_key" "cmk_secrets" {
  description             = format(local.cmk_description_template, "secrets", var.project, var.environment)
  deletion_window_in_days = local.cmk_deletion_window_in_days
  enable_key_rotation     = local.cmk_enable_key_rotation
  tags                    = merge(var.tags, tomap({"Name" = format("%s-cmk-secrets-%s", var.project, var.environment)}))
}

resource "aws_kms_alias" "cmk_secrets_alias" {
  name          = format("alias/%s-cmk-secrets-%s", var.project, var.environment)
  target_key_id = aws_kms_key.cmk_rds.id
}

resource "aws_kms_key" "cmk_s3pipeline" {
  count                   = var.environment == "prod" ? 0 : 1
  description             = format(local.cmk_description_template, "s3pipeline", var.project, var.environment)
  deletion_window_in_days = local.cmk_deletion_window_in_days
  enable_key_rotation     = local.cmk_enable_key_rotation
  tags                    = merge(var.tags, tomap({"Name" = format("%s-cmk-s3pipeline-%s", var.project, var.environment)}))
  policy                  = data.aws_iam_policy_document.codepipeline-access.json
}

resource "aws_kms_alias" "cmk_s3pipeline_alias" {
  count         = var.environment == "prod" ? 0 : 1
  name          = format("alias/%s-cmk-s3pipeline-%s", var.project, var.environment)
  target_key_id = aws_kms_key.cmk_s3pipeline[count.index].id
}
