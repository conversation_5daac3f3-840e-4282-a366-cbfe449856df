variable "environment" {
  description = "Environment the VPC represents"
}

variable "project" {
  description = "Project owning these resources"
}

variable "vpc_id" {
  description = "ID of the VPC for this networking configuration."
}

variable "tags" {
  type        = map(any)
  description = "Tags for resources contained in this module"
}

variable "vpc_database_subnets" {
  type        = list(any)
  description = "Subnets where Database will get IP addresses from"
}

variable "eks_worker_sg" {
  type        = string
  description = "Security group attached to EKS workers"
}

variable "reporting_database_subnets" {
  type        = list(string)
  description = "CIDR of reporting database subnets"
}

variable "public_subnets" {
  type        = list(string)
  description = "CIDR of bastion host subnets"
}

variable "vpc_management_subnets" {
  type        = list(string)
  description = "CIDR of management subnets"
}

variable "ptc_it_on_prem_networks" {
  type        = list(any)
  description = "CIDR Ranges for PTC IT On Prem networks"
}
