resource "aws_security_group" "windows" {
  count       = var.environment == "qa" ? 1 : 0
  name        = "${var.project}-windows-sg-${var.environment}"
  description = "Windows public access"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 3389
    to_port     = 3389
    protocol    = "tcp"
    description = "Windows access on RDP type"
    cidr_blocks = ["*************/29"]
  }

  ingress {
    from_port   = 3389
    to_port     = 3389
    protocol    = "tcp"
    description = "Windows access on RDP type for Prisma VPN"
    cidr_blocks = ["*********/16"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(var.tags, tomap({ "Name" = format("%s-windows-sg-%s", var.project, var.environment) }))
}
resource "aws_security_group" "postgres_access" {
  name        = "${var.project}-postgres-access-sg-${var.environment}"
  description = "SG postgres DB"
  vpc_id      = var.vpc_id

  egress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    description = "Postgres access on 5432"
    cidr_blocks = var.vpc_database_subnets
  }

  tags = merge(var.tags, tomap({ "Name" = format("%s-postgres-access-%s", var.project, var.environment) }))
}

resource "aws_security_group" "postgres" {
  name        = "${var.project}-postgres-sg-${var.environment}"
  description = "SG postgres DB"
  vpc_id      = var.vpc_id


  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    description = "Postgres access on 5432"

    security_groups = flatten([
      aws_security_group.postgres_access.id,
      aws_security_group.connect_server.id,
      aws_security_group.bastion.id,
      var.environment != "prod" ? [aws_security_group.non_prod_connect_server[0].id] : [],
      var.eks_worker_sg,
    ])
  }
  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    description = "Allow access for replication to enterprise-reporting"
    cidr_blocks = var.reporting_database_subnets
  }

  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    description = "Allow access for RDS within management subnets"
    cidr_blocks = var.vpc_management_subnets
  }

  lifecycle {
   ignore_changes = ["ingress"]
  }

  tags = merge(var.tags, tomap({ "Name" = format("%s-postgres-%s", var.project, var.environment) }))
}

resource "aws_security_group" "redis_access" {
  name        = "${var.project}-redis-access-sg-${var.environment}"
  description = "SG Redis Access"
  vpc_id      = var.vpc_id

  egress {
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    description = "Redis access on 6379"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, tomap({ "Name" = format("%s-redis-access-%s", var.project, var.environment) }))
}

resource "aws_security_group" "redis" {
  name        = "${var.project}-redis-sg-${var.environment}"
  description = "SG Redis DB"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    description = "Allows Redis Access 6379"

    security_groups = [
      var.eks_worker_sg,
    ]
  }

  tags = merge(var.tags, tomap({ "Name" = format("%s-redis-%s", var.project, var.environment) }))
}

resource "aws_security_group" "connect_server" {
  name        = format("%s-connect-server-sg-%s", var.project, var.environment)
  description = "Security group for bastion machine"
  vpc_id      = var.vpc_id

  # SSH access on bastion machine
  #   ingress {
  #     from_port = 22
  #     to_port   = 22
  #     protocol  = "tcp"
  #     cidr_blocks = ["0.0.0.0/0"]
  #   }
  ingress {
    description = "Vancouver VPN/Office - Sec. Group: PCI-users"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**********/24"]
  }
  ingress {
    description = "Vancouver VPN/Office - Sec. Group: Corp Contractors"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**********/24"]
  }
  ingress {
    description = "Toronto VPN/Office - Sec. Group: PCI-users"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["***********/24"]
  }
  ingress {
    description = "Toronto VPN/Office - Sec. Group: Corp Contractors"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["***********/24"]
  }
  ingress {
    description = "prisma-vpn"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*********/16"]
  }


  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  lifecycle {
    create_before_destroy = true
    ####  ignore_changes        = ["ingress"]
  }

  tags = merge(var.tags, tomap({ "Name" = format("%s-connect-server-sg-%s", var.project, var.environment) }))
}

resource "aws_security_group" "non_prod_connect_server" {
  count       = var.environment == "prod" ? 0 : 1
  name        = format("%s-connect-server-sg-%s-restricted", var.project, var.environment)
  description = "Security group for bastion machine"
  vpc_id      = var.vpc_id

  # SSH access on bastion machine
  ingress {
    description = "Vancouver VPN/Office - Sec. Group: PCI-users"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**********/24"]
  }
  ingress {
    description = "Vancouver VPN/Office - Sec. Group: Corp Contractors"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**********/24"]
  }
  ingress {
    description = "Toronto VPN/Office - Sec. Group: PCI-users"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["***********/24"]
  }
  ingress {
    description = "Toronto VPN/Office - Sec. Group: Corp Contractors"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["***********/24"]
  }
  ingress {
    description = "prisma-vpn"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*********/16"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  lifecycle {
    create_before_destroy = true
    ####  ignore_changes        = ["ingress"]
  }

  tags = merge(var.tags, tomap({ "Name" = format("%s-connect-server-sg-%s", var.project, var.environment) }))
}
resource "aws_security_group" "eks_node_access" {
  name_prefix = "${var.project}-${var.environment}-eks-ssh-access"
  description = "Allow SSH access from Bastion"
  vpc_id      = var.vpc_id

  ingress {
    description = "SSH access"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.public_subnets
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = var.tags
}

resource "aws_security_group" "bastion" {
  name        = "${var.project}-bastion-sg-${var.environment}"
  description = "Allow SSH inbound traffic"
  vpc_id      = var.vpc_id

  # SSH access on bastion machine
  ingress {
    from_port = 22
    to_port   = 22
    protocol  = "tcp"

    cidr_blocks = var.ptc_it_on_prem_networks
  }

  ingress {
    from_port = 10050
    to_port   = 10050
    protocol  = "tcp"

    cidr_blocks = ["*************/32"]
  }

  ingress {
    from_port = -1
    to_port   = -1
    protocol  = "icmp"

    cidr_blocks = ["*************/32"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = var.tags
}
