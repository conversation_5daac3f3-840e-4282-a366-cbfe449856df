#output "app_server_sg" {
#  value = aws_security_group.app_server.*.id
#}

output "connect_server_sg" {
  value = aws_security_group.connect_server.id
}

output "non_prod_connect_server_sg" {
  value = aws_security_group.non_prod_connect_server[*].id
}
output "windows_sg" {
  value = aws_security_group.windows[*].id
}

output "postgres_sg" {
  value = aws_security_group.postgres.id
}

output "postgres_access_sg" {
  value = aws_security_group.postgres_access.id
}

output "redis_sg" {
  value = aws_security_group.redis.id
}

output "redis_access_sg" {
  value = aws_security_group.redis_access.id
}

output "eks_node_access_sg" {
  value = aws_security_group.eks_node_access.id
}

output "bastion_sg_id" {
  value = aws_security_group.bastion.id
}
