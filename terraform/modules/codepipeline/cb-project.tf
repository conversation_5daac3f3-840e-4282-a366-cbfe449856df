resource "aws_iam_role" "gl-cb-role" {
  name = "codebuild-gl-build-project-main-service-role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codebuild.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "gl-artifact-policy" {
  role = aws_iam_role.gl-cb-role.name

  policy = <<POLICY
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Resource": [
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/gl-build-project-main",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/gl-build-project-main:*",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/ledger-account-build",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/ledger-account-build:*",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/ledger-profile-build",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/ledger-profile-build:*",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/ledger-external-account-build",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/ledger-external-account-build:*",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/ledger-schedulers-build",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/ledger-schedulers-build:*",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/ledger-transaction-build",
                "arn:aws:logs:ca-central-1:************:log-group:/aws/codebuild/ledger-transaction-build:*"
            ],
            "Action": [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents"
            ]
        },
        {
            "Effect": "Allow",
            "Resource": [
                "arn:aws:s3:::gl-codepipeline-qa*"
            ],
            "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:GetObjectVersion",
                "s3:GetBucketAcl",
                "s3:GetBucketLocation"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "codebuild:CreateReportGroup",
                "codebuild:CreateReport",
                "codebuild:UpdateReport",
                "codebuild:BatchPutTestCases",
                "codebuild:BatchPutCodeCoverages"
            ],
            "Resource": [
                "arn:aws:codebuild:ca-central-1:************:report-group/*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "ecr:GetRegistryPolicy",
                "ecr:CreateRepository",
                "ecr:DescribeRegistry",
                "ecr:DescribePullThroughCacheRules",
                "ecr:GetAuthorizationToken",
                "ecr:PutRegistryScanningConfiguration",
                "ecr:CreatePullThroughCacheRule",
                "ecr:DeletePullThroughCacheRule",
                "ecr:PutRegistryPolicy",
                "ecr:GetRegistryScanningConfiguration",
                "ecr:BatchImportUpstreamImage",
                "ecr:DeleteRegistryPolicy",
                "ecr:PutReplicationConfiguration"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": "ecr:*",
            "Resource": "arn:aws:ecr:*:************:repository/*"
        },
        {
          "Effect": "Allow",
          "Action": [
            "kms:*"
          ],
          "Resource": "${var.kms_key_arn}"
        },
        {
            "Effect": "Allow",
            "Action": "ec2:*",
            "Resource": "*"
        }
    ]
}
POLICY
}

resource "aws_codebuild_project" "gl-build-project-main" {
  name          = "gl-build-project-main"
  description   = "GL qa codebuild project"
  build_timeout = "5"
  service_role  = aws_iam_role.gl-cb-role.arn

  artifacts {
    type = "CODEPIPELINE"
  }

  cache {
    type = "NO_CACHE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:3.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"

  }
  logs_config {
    cloudwatch_logs {
      group_name  = "/aws/codebuild/gl-build-project-main"
      stream_name = "log-stream"
    }

  }

  source {
    type = var.source_type
  }

}

