resource "aws_codebuild_project" "ledger-profile-build" {
  name          = "ledger-profile-build"
  description   = "GL profile qa codebuild project"
  build_timeout = "5"
  service_role  = aws_iam_role.gl-cb-role.arn

  artifacts {
    type = "CODEPIPELINE"
  }

  cache {
    type = "NO_CACHE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:5.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true
  }
  logs_config {
    cloudwatch_logs {
      group_name  = "/aws/codebuild/ledger-profile-build"
      stream_name = "log-stream"
    }

  }

  source {
    type = var.source_type
    buildspec = "buildspec_ledger-profile.yml"
  }

  vpc_config {
    vpc_id = var.codebuild_vpc_id

    subnets = var.codebuild_private_subnets
    security_group_ids = var.codebuild_sg_id
  }
}

