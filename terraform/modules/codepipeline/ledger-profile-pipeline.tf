resource "aws_codepipeline" "ledger-profile-pipeline" {
  name     = "ledger-profile-pipeline"
  role_arn = aws_iam_role.codepipeline_role.arn

  artifact_store {
    location = aws_s3_bucket.codepipeline_bucket.bucket
    type     = "S3"

      encryption_key {
        id   = var.kms_key_arn
        type = "KMS"
      }
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeStarSourceConnection"
      version          = "1"
      output_artifacts = ["source_output"]

      configuration = {
        ConnectionArn        = aws_codestarconnections_connection.gl-bitbucket.arn
        FullRepositoryId     = "peoplestrust/general_ledger"
        BranchName           = "develop"
        OutputArtifactFormat = "CODE_ZIP"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = aws_codebuild_project.ledger-profile-build.name
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      input_artifacts  = ["source_output"]
      output_artifacts = ["build_output"]
      version          = "1"

      configuration = {
        ProjectName = aws_codebuild_project.ledger-profile-build.name
      }
    }
  }
}
