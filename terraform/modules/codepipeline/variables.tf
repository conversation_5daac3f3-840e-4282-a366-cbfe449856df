variable "project" {
  type = string
}
variable "codepipeline_bucket_name" {
  type = string
}
variable "codepipeline_role_name" {
  type = string
}
variable "kms_key_arn" {
  type = string
}
variable "source_type" {
  type    = string
  default = "CODEPIPELINE"
}
variable "codebuild_vpc_id" {
  type = string
}
variable "codebuild_private_subnets" {
  type = list
}
variable "codebuild_sg_id" {
  type = list
}
