variable "environment" {
  description = "Environment where this resource is deployed"
}

variable "project" {
  description = "Project that owns this resource"
}

variable "tags" {
  type = map(string)
}

# # variable "kms_key_arn" {
# #   description = "KMS keys for each environment"
# # }

variable "lambda_arn" {
  description = "ARN of the Lambda function to be used with this module"
  type        = string
}