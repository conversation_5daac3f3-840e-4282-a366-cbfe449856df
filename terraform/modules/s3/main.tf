resource "aws_s3_bucket" "nlb_access_logs" {
  bucket = "${var.project}-nlb-access-logs-bucket-${var.environment}"
  acl    = "private"

  tags = var.tags
}

resource "aws_s3_bucket_policy" "nlb_access_logs_policy" {
  bucket = aws_s3_bucket.nlb_access_logs.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.nlb_access_logs.arn}/*"
        Condition = {
          StringEquals = {
            "s3:x-amz-acl" = "bucket-owner-full-control"
          }
        }
      },
      {
        Effect = "Allow"
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        }
        Action   = "s3:GetBucketAcl"
        Resource = aws_s3_bucket.nlb_access_logs.arn
      }
    ]
  })
}

resource "aws_s3_bucket" "recon_mft" {
  count  = var.environment == "prod" ? 0 : 1
  bucket = "pg-gl-recon-${var.environment}"
  acl    = "private"

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = var.tags
}

resource "aws_s3_bucket_public_access_block" "recon_mft" {
  count  = var.environment == "prod" ? 0 : 1
  bucket = aws_s3_bucket.recon_mft[count.index].id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_notification" "lambda_trigger" {
  count  = var.environment == "prod" ? 0 : 1
  bucket = aws_s3_bucket.recon_mft[count.index].id

  lambda_function {
    lambda_function_arn = var.lambda_arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "input/"
  }

}
