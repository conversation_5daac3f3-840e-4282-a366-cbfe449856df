data "aws_ec2_transit_gateway" "tgw" {
  id = var.transit_gateway_id
}

resource "aws_ec2_transit_gateway_vpc_attachment" "general-ledger" {
 vpc_id                                          = var.vpc_id
 subnet_ids                                      = var.subnet_ids
 transit_gateway_id                              = data.aws_ec2_transit_gateway.tgw.id
 transit_gateway_default_route_table_association = false
 transit_gateway_default_route_table_propagation = false
 dns_support                                     = var.dns_support

  #We need this because of this bug: https://github.com/terraform-providers/terraform-provider-aws/issues/9070
 lifecycle {
   ignore_changes = [transit_gateway_default_route_table_association, transit_gateway_default_route_table_propagation]
 }

 tags = merge(var.tags, tomap({"Name" = format("%s-tgw-vpc-attachment-%s", var.project, var.environment)}))
}
