variable "dns_support" {
  description = "Whether to enable or not dns support for this VPC attachment"
}

variable environment {
  description = "Environment the VPC represents"
}

variable "project" {
  description = "Project owning these resources"
}

variable "subnet_ids" {
  type        = list
  description = "Subnets that will be part of this VPC attachment"
}

variable "transit_gateway_id" {
  description = "Transit gateway where VPCs are being attached"
}

variable "vpc_id" {
  description = "ID of VPC to attach to the transit gateway"
}

variable "tags" {
  type        = map
  description = "Tags for resources contained in this module"
}
