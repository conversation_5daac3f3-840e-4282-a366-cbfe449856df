######################
# SSM Session Manager
######################

data "aws_iam_policy_document" "ec2_assume_policy" {
  statement {
    actions = [
      "sts:AssumeRole",
    ]

    principals {
      type = "Service"

      identifiers = [
        "ec2.amazonaws.com",
      ]
    }
  }
}

resource "aws_iam_role_policy_attachment" "ssm_session_manager" {
  role       = aws_iam_role.ssm_session_manager.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}


resource "aws_iam_role" "ssm_session_manager" {
  name               = "${var.project}-session-manager-role-${var.environment}"
  assume_role_policy = data.aws_iam_policy_document.ec2_assume_policy.json
  tags               = var.tags
}

resource "aws_iam_instance_profile" "ec2_ssm" {
  name = "${var.project}-session-manager-instance-profile-${var.environment}"
  role = aws_iam_role.ssm_session_manager.name
}

module "bastion_instance" {
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "~> 3.0"

  name = format("%s-bastion-%s", var.project, var.environment)

  ami                         = "ami-037d1030a0db1fa12"
  instance_type               = "t3.small"
  monitoring                  = true
  vpc_security_group_ids      = [var.bastion_sg_id]
  subnet_id                   = var.bastion_subnet_id
  associate_public_ip_address = false
  iam_instance_profile        = aws_iam_instance_profile.ec2_ssm.id

  tags = merge(var.tags, { DatadogAllowed = false})
}

resource "aws_s3_bucket" "ssm" {
  bucket = "pg-${var.project}-ssm-${var.environment}"
  tags = var.tags
}

resource "aws_s3_bucket_server_side_encryption_configuration" "ssm_s3" {
  bucket = aws_s3_bucket.ssm.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = "AES256"
    }
  }
  depends_on = [aws_s3_bucket.ssm]
}

################
#   EC2 ROLE   #
################


data "aws_iam_policy_document" "ec2_ssm_for_s3" {

    statement {
    actions = [
      "s3:ListBucket",
      "s3:GetBucketLocation",
      "ssm:StartSession"
    ]

    resources = [
      "arn:aws:s3:::pg-${var.project}-ssm-${var.environment}",
    ]
  }

  statement {
    actions = [
      "s3:PutObject",
      "s3:GetObject",
      "s3:DeleteObject",
    ]

    resources = [
      "arn:aws:s3:::pg-${var.project}-ssm-${var.environment}/*"
    ]
  }
}

resource "aws_iam_policy" "ec2_ssm_for_s3" {
  name        = "${var.project}-ec2-policy-${var.environment}"
  path        = "/"
  description = "Allows s3 for EC2 Instance"
  policy      = "${data.aws_iam_policy_document.ec2_ssm_for_s3.json}"
}

resource "aws_iam_role_policy_attachment" "attach" {
  role       = aws_iam_role.ssm_session_manager.name
  policy_arn = aws_iam_policy.ec2_ssm_for_s3.arn
}