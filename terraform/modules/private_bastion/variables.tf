variable "project" {
  type = string
}

variable "bastion_sg_id" {
  type = string
}

variable "vpc_id" {
  type = string
}

variable "vpc_region" {
  type = string
}

variable "environment" {
  type = string
}

variable "bastion_subnet_id" {
  type = string
}

variable "tags" {
  type = map(string)
}

variable key_name{
  type = string
}

variable "private_ip" {
  description = "Private IP address to associate with the instance in a VPC"
  default     = ""
}