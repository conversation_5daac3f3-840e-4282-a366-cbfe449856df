data "aws_subnet" "application" {
  count      = length(local.vpc_application_subnets)
  cidr_block = element(local.vpc_application_subnets, count.index)
  depends_on = [
    module.vpc[0]
  ]
}

locals {
  application_subnet_ids = concat(data.aws_subnet.application.*.id, [])
  private_subnet_ids     = local.environment == "dev" ? [] : module.vpc[0].private_subnets
  management_subnet_id   = local.environment == "dev" ? "" : element(local.private_subnet_ids, length(local.private_subnet_ids) - 2)
  # aws_auth_configmap_username = element(split("_", local.sso_role_arn), 1)
  aws_auth_configmap_username = [
    for role_arns in values(var.env_to_sso_role_arn) : [
      for arn in role_arns : element(split("_", arn), 1)
    ]
  ]
}

module "tags" {
  # source = "git::https://bitbucket.org/peoplestrust/shared-infrastructure//shared//terraform//modules//common_tags?ref=1.0.0"
  source = "../../../modules/common_tags"
  count  = local.environment == "dev" ? 1 : 1

  environment = local.environment
  project     = local.project
}

module "tol_tags" {
  source = "../../../modules/tol_tags"
  count  = local.environment == "dev" ? 1 : 1

  environment = local.environment
}

module "keys" {
  # source = "git::https://bitbucket.org/peoplestrust/shared-infrastructure//shared//terraform//modules//keys?ref=1.0.0"
  source  = "terraform-aws-modules/key-pair/aws"
  count   = local.environment == "dev" ? 1 : 1
  version = "1.0.1"

  key_name   = "${local.project}-${local.environment}"
  public_key = local.ec2_public_key
  tags       = module.tags[count.index].tags
}

module "vpc" {

  source               = "terraform-aws-modules/vpc/aws"
  count                = local.environment == "dev" ? 1 : 1
  version              = "2.78.0"
  enable_dns_hostnames = true

  name = "${local.project}-vpc-${local.environment}"
  cidr = local.vpc_cidr
  azs  = var.vpc_azs

  public_subnets = local.vpc_public_subnets
  private_subnets = concat(
    local.vpc_application_subnets,
    local.vpc_internal_subnets,
    local.vpc_event_subnets,
    local.vpc_management_subnets
  )
  database_subnets = local.vpc_database_subnets

  enable_s3_endpoint     = true
  enable_nat_gateway     = false
  single_nat_gateway     = false
  one_nat_gateway_per_az = true

  tags = module.tags[count.index].tags

  private_subnet_tags = {
    "kubernetes.io/role/internal-elb"                 = 1
    "kubernetes.io/cluster/${local.eks_cluster_name}" = "shared"
  }
  public_subnet_tags = {
    "kubernetes.io/role/elb" = 1
    "KubernetesCluster"      = local.eks_cluster_name
  }
}
module "vpc_additional_routes" {
  source = "../../../modules/vpc_additional_routes"
  count  = 1

  environment     = local.environment
  project         = local.project
  route_table_ids = module.vpc[count.index].private_route_table_ids

  cidr_blocks = local.vpc_additional_routes

  tgw_id = var.tgw_id

  tags = module.tags[count.index].tags
}

module "iam" {
  source = "../../../modules/iam"
  # source  = "terraform-aws-modules/iam/aws"
  # version = "2.25.0"
  count                 = 1
  environment           = local.environment
  project               = local.project
  vpc_region            = var.vpc_region
  account_id            = local.aws_account_number
  tol_recon_mft_s3_name = module.s3[count.index].mft_recon_bucket_name
}

module "vpc_flow_logs" {
  # source = "git::https://bitbucket.org/peoplestrust/shared-infrastructure//shared//terraform//modules//vpc_flow_logs?ref=0.2.4"
  source = "../../../modules/vpc_flow_logs"
  count  = 1

  enable_flow_logs       = true
  environment            = local.environment
  flow_logs_iam_role_arn = module.iam[count.index].vpc_flow_logs_role_arn
  project                = local.project
  traffic_type           = "ALL"
  vpc_id                 = module.vpc[count.index].vpc_id
}

module "networking" {
  source = "../../../modules/networking"
  count  = 1

  environment                = local.environment
  project                    = local.project
  vpc_id                     = module.vpc[count.index].vpc_id
  tags                       = module.tags[count.index].tags
  vpc_database_subnets       = local.vpc_database_subnets
  eks_worker_sg              = module.EKS_cluster[count.index].cluster_primary_security_group_id
  reporting_database_subnets = local.reporting_database_subnets
  vpc_management_subnets     = local.vpc_management_subnets
  public_subnets             = local.vpc_public_subnets
  ptc_it_on_prem_networks    = var.ptc_it_on_prem_networks
}

module "transit_gateway_attachment" {
  source = "../../../modules/transit_gateway_attachment"
  count  = 1

  environment        = local.environment
  project            = local.project
  dns_support        = "enable"
  transit_gateway_id = var.tgw_id

  # Awful workaround to overcome this limitation https://github.com/hashicorp/terraform/issues/18259#issuecomment-438407005
  subnet_ids = module.vpc[count.index].public_subnets

  vpc_id = module.vpc[count.index].vpc_id

  tags = module.tags[count.index].tags
}

module "connect_server" {
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "2.16.0"
  # version                     = "4.2.1"
  count                       = 1
  associate_public_ip_address = false
  name                        = "ledger-${local.environment}-connect-server"
  instance_count              = 1
  ami                         = "ami-0d0fce9254ae31bc8"
  instance_type               = "t3a.small"
  vpc_security_group_ids      = local.environment == "prod" ? [module.networking[count.index].connect_server_sg] : [module.networking[count.index].non_prod_connect_server_sg[count.index]]
  # vpc_security_group_ids = [module.networking[count.index].connect_server_sg]
  subnet_ids              = local.environment == "prod" ? [local.management_subnet_id] : module.vpc[count.index].private_subnets
  key_name                = module.keys[count.index].key_pair_key_name
  use_num_suffix          = true
  disable_api_termination = true
  private_ip              = local.connect_server_ip_addresses
  iam_instance_profile    = module.iam[count.index].ec2_instance_profile_name
  root_block_device = [
    {
      volume_type = "gp2"
      volume_size = 50
    },
  ]

  tags = merge({ "DatadogAllowed" = "false" }, module.tags[count.index].tags)
  volume_tags = merge(
    {
      "Name" = "${local.project}-volume-connect-server-${local.environment}"
    },
    module.tags[count.index].tags,
  )
}

#######################
##   BASTION        ###
#######################

# module "bastion" {
#   source = "../../../modules/private_bastion"
#   #count  = local.environment == "dev" ? 1 : 0

#   count             = 1
#   bastion_subnet_id = data.aws_subnet.management[0].id
#   environment       = local.environment
#   project           = local.project

#   vpc_id        = module.vpc[0].vpc_id
#   vpc_region    = var.vpc_region
#   bastion_sg_id = module.networking[0].bastion_sg_id

#   tags = module.tags[count.index].tags
# }

#####################
#   MS Windows      #
#####################

data "aws_subnet" "management" {
  count      = length(local.vpc_management_subnets)
  cidr_block = element(local.vpc_management_subnets, count.index)
}
locals {
  hosts = {
    windows = {
      ami_id        = "ami-04e616725b0fb47ef"
      instance_type = "t3a.2xlarge"
      public_key    = local.ec2_public_key
    }
  }
}


module "windows" {
  source   = "../../../modules/private_bastion_ssm"
  for_each = local.hosts
  #  count  = local.environment == "qa" ? 1 : 0

  name              = each.key
  ami_id            = each.value["ami_id"]
  instance_type     = each.value["instance_type"]
  bastion_subnet_id = data.aws_subnet.management[0].id
  environment       = local.environment
  project           = local.project
  vpc_id            = module.vpc[0].vpc_id
  vpc_region        = var.vpc_region
  bastion_sg_id     = module.networking[0].windows_sg
  public_key        = each.value["public_key"]

  additional_iam_policies = [
    #  local.bastion_s3_read
  ]

  tags = merge({ "DatadogAllowed" = "false" }, module.tags[0].tags)
}

module "secrets" {
  source      = "../../../modules/secrets"
  count       = 1
  project     = local.project
  environment = local.environment
}



#################
#   EKS MASTER  #
#################

module "EKS_cluster" {
  source                          = "terraform-aws-modules/eks/aws"
  version                         = "18.30.2"
  count                           = 1
  create                          = true
  cluster_name                    = local.eks_cluster_name
  cluster_version                 = local.environment == "prod" ? "1.31" : "1.31"
  subnet_ids                      = local.application_subnet_ids
  vpc_id                          = module.vpc[count.index].vpc_id
  manage_aws_auth_configmap       = true
  create_aws_auth_configmap       = false
  enable_irsa                     = true
  cluster_endpoint_private_access = true
  cluster_endpoint_public_access  = true
  cluster_tags                    = module.tags[count.index].tags
  create_cloudwatch_log_group     = local.environment == "dev" ? false : true

  cluster_enabled_log_types = [
    "audit",
    "api",
    "authenticator",
    "controllerManager"
  ]

  cluster_addons = {
    coredns = {
      resolve_conflicts = "OVERWRITE"
    }
    kube-proxy = {}
    vpc-cni = {
      resolve_conflicts = "OVERWRITE"
    }
  }

  cluster_security_group_additional_rules = {
    egress_nodes_ephemeral_ports_tcp = {
      description                = "To node 1025-65535"
      protocol                   = "tcp"
      from_port                  = 1025
      to_port                    = 65535
      type                       = "egress"
      source_node_security_group = true
    }
  }
  node_security_group_additional_rules = {
    ingress_self_all = {
      description = "Node to node all ports/protocols"
      protocol    = "-1"
      from_port   = 0
      to_port     = 0
      type        = "ingress"
      self        = true
    }
    ingress_cluster_all = {
      description                   = "Cluster to node all ports/protocols"
      protocol                      = "-1"
      from_port                     = 0
      to_port                       = 0
      type                          = "ingress"
      source_cluster_security_group = true
    }
    egress_all = {
      description      = "Node all egress"
      protocol         = "-1"
      from_port        = 0
      to_port          = 0
      type             = "egress"
      cidr_blocks      = ["0.0.0.0/0"]
      ipv6_cidr_blocks = ["::/0"]
    }
  }
  eks_managed_node_group_defaults = {
    disk_size                  = local.eks_instance.disk_size
    instance_types             = [local.eks_instance.instance_type]
    ami_release_version        = local.environment == "prod" ? "1.31.7-20250519" : "1.31.7-20250505"
    iam_role_attach_cni_policy = true
  }

  eks_managed_node_groups = {
    # blue = {}
    green = {
      # name                   = "${(local.project)-$(local.environment)}-EKS"
      min_size               = local.eks_asg_min_size
      max_size               = local.eks_asg_max_size
      desired_size           = local.eks_asg_desired_capacity
      instance_types         = [local.eks_instance.instance_type]
      create_launch_template = false
      launch_template_name   = ""
      iam_role_additional_policies = [
        module.iam[count.index].eks_nodes_access_s3_bucket_common_services_arn,
        module.iam[count.index].kubernetes_cloudwatch_logs_arn,
        module.iam[count.index].fluent_bit_iam_policy_arn
      ]

      remote_access = {
        ec2_ssh_key               = module.keys[count.index].key_pair_key_name
        source_security_group_ids = [module.networking[0].eks_node_access_sg]
      }
      tags = {
        "Name"                                                = "${local.project}-${local.environment}-eks"
        "k8s.io/cluster-autoscaler/enabled"                   = "true"
        "k8s.io/cluster-autoscaler/${local.eks_cluster_name}" = "true"
        "Environment"                                         = local.environment
        "Project"                                             = local.project
      }
    }
  }
  aws_auth_roles = flatten([
    for env, role_arns in var.env_to_sso_role_arn :
    [
      for arn in role_arns :
      (
        arn == "arn:aws:iam::${local.aws_account_number}:role/tol-lambda-eks-${local.environment}"
        ? {
          rolearn  = arn
          username = "lambda-job-creator"
          groups   = ["lambda-job-creators"]
        }
        : {
          rolearn  = arn
          username = element(split("_", arn), 1)
          groups   = [local.aws_auth_configmap_group]
        }
      )
      if env == local.environment
    ]
  ])

  tags = module.tags[count.index].tags
}

module "cluster_autoscaler" {
  source = "../../../modules/cluster_autoscaler"
  count  = 1

  # Update version of chart accordingly to application version which is equals to EKS version
  # https://artifacthub.io/packages/helm/cluster-autoscaler/cluster-autoscaler/9.44.0#:~:text=CHANGELOG-,APPLICATION%20VERSION,-1.31.0
  # export KUBE_CONFIG_PATH=~/.kube/config
  helm_chart_version               = "9.44.0" # Application Version 1.31
  cluster_name                     = module.EKS_cluster[count.index].cluster_id
  cluster_identity_oidc_issuer     = module.EKS_cluster[count.index].cluster_oidc_issuer_url
  cluster_identity_oidc_issuer_arn = module.EKS_cluster[count.index].oidc_provider_arn
}

module "scheduled_scaling" {
  source = "../../../modules/nodes_scheduling"

  count    = local.environment == "qa" ? 1 : 0
  asg_name = module.EKS_cluster[count.index].eks_managed_node_groups_autoscaling_group_names[0]

  asg_min_size         = 0
  asg_desired_capacity = 3
  asg_max_size         = 4
}

module "kms" {
  source         = "../../../modules/kms"
  count          = 1
  project        = local.project
  environment    = local.environment
  account_number = local.aws_account_number
  tags           = module.tags[count.index].tags
}

module "lambda" {
  source                     = "../../../modules/lambda"
  count                      = local.environment != "prod" ? 1 : 0
  name                       = "tol-lambda-eks-${local.environment}"
  account_id                 = local.aws_account_number
  image_id                   = local.environment == "qa" ? "**************" : "**************"
  environment                = local.environment
  eks_name                   = local.eks_cluster_name
  region                     = var.vpc_region
  s3_bucket_notification_arn = "arn:aws:s3:::pg-gl-recon-${local.environment}"
  tags                       = module.tol_tags[count.index].tol_tags
}

module "tol_lambda_auth0" {
  source = "../../../modules/tol_lambda_auth0"
  count  = local.environment != "prod" ? 1 : 0

  name               = "tol-lambda-auth0-${local.environment}"
  environment        = local.environment
  region             = var.vpc_region
  aws_account_number = local.aws_account_number
  s3_bucket          = module.s3[count.index].mft_recon_bucket_name
  s3_key             = "lambda_package_bedrock.zip"
  tags               = module.tol_tags[count.index].tol_tags
}


module "s3" {
  source      = "../../../modules/s3"
  count       = 1
  environment = local.environment
  project     = local.project
  tags        = module.tags[count.index].tags
  lambda_arn  = module.lambda[count.index].lambda_function_arn
}

module "aurora_postgres" {
  # source = "git::https://bitbucket.org/peoplestrust/shared-infrastructure//shared//terraform//modules//aurora?ref=1.0.2"
  source                            = "../../../modules/aurora"
  count                             = 1
  create_db_cluster                 = true
  create_db_cluster_parameter_group = true
  availability_zones                = var.vpc_azs
  cluster_identifier                = local.environment == "qa" ? "${local.project}-db-dev${local.environment}" : "${local.project}-db-${local.environment}"
  db_deletion_protection            = local.environment == "dev" ? false : true
  cluster_instance_count            = "1"
  engine                            = "aurora-postgresql"
  storage_encrypted                 = true
  auto_minor_version_upgrade        = false
  # encryption_kms_key_id = local.environment == "prod" ? "arn:aws:kms:ca-central-1:053126927946:alias/general-ledger-cmk-rds-prod" : module.kms[count.index].cmk_rds_arn
  engine_version          = local.environment == "prod" ? "14.15" : "14.15"
  instance_class          = local.db_instance_class
  encryption_kms_key_id   = local.environment == "prod" ? "arn:aws:kms:ca-central-1:053126927946:key/aee17f9b-4808-465a-a61b-57ebcd40b4b0" : module.kms[count.index].cmk_rds_arn
  database_name           = local.environment == "qa" || local.environment == "dev" ? "${local.project}_db_devqa" : "${local.project}_db_${local.environment}"
  master_username         = local.environment == "qa" || local.environment == "dev" ? "${local.project}_user_devqa" : "${local.project}_user_${local.environment}"
  snapshot_identifier     = local.environment == "dev" ? "rds:gl-db-devqa-2025-09-11-07-41" : null
  master_password         = module.secrets[count.index].postgres_master_password
  port                    = "5432"
  vpc_security_group_ids  = [module.networking[count.index].postgres_sg]
  maintenance_window      = "Sun:06:00-Sun:07:00"
  backup_window           = "07:30-08:30"
  backup_retention_period = local.environment == "prod" ? "7" : "1"

  # Subnets must come from VPC module
  db_subnet_group_name = module.vpc[count.index].database_subnet_group
  family               = "aurora-postgresql14"

  # Snapshot name upon DB deletion
  final_snapshot_identifier = "${local.project}-db-${local.environment}-final-snapshot-1"
  apply_immediately         = "true"

  # NOTE:  Comment during first-time DB creation as this creates a circular dependency between rds_clusters and the cluster parameter group.
  parameters = [
    {
      name         = "rds.force_ssl"
      value        = "1"
      apply_method = "pending-reboot"
    },
    {
      name         = "timezone"
      value        = "us/eastern"
      apply_method = "pending-reboot"
    },
    {
      name         = "wal_sender_timeout"
      value        = "0"
      apply_method = "immediate"
    },
    {
      name         = "rds.logical_replication"
      value        = "1"
      apply_method = "pending-reboot"
    },
    {
      name         = "shared_preload_libraries"
      value        = "pg_stat_statements,pg_cron"
      apply_method = "pending-reboot"
    },
    {
      apply_method = "immediate"
      name         = "log_hostname"
      value        = "1"
    }
  ]

  tags = module.tags[count.index].tags
}

# ########### #
# API GATEWAY #
# ########### #
# This data is for QA env which doesn't have its only cloud resources.
data "aws_eks_cluster" "gl-eks-main-devqa" {
  count = local.environment == "qa" ? 1 : 0
  name  = local.eks_cluster_name
}
locals {
  oidc_provider = try(replace(data.aws_eks_cluster.gl-eks-main-devqa[0].identity[0].oidc[0].issuer, "https://", ""), "")
}
module "K8s_services" {
  source                      = "../../../modules/k8s_alb_ingress_services"
  count                       = 1
  create_management_namespace = false
  create_namespace            = true
  project                     = local.project
  environment                 = local.environment
  oidc_provider               = module.EKS_cluster[count.index].oidc_provider
  aws_account                 = local.aws_account_number
  tags                        = module.tags[count.index].tags
  namespace                   = "pg-ledger-${local.environment}"
  domain_name                 = local.domain_name
  service_names = {
    "/v1/ledger/account"           = "account-service",
    "/v1/ledger/account/external/" = "account-external-service",
    "/v1/ledger/profile"           = "profile-service",
    "/v1/ledger/transaction"       = "transaction-service",
    "/v1/ledger/transaction/async" = "transaction-async-service",
    "/v1/ledger/health"            = "health-service",
  }
  # ecr_repo_names = [
  #   "ledger-schedulers",
  # ]
}
data "aws_subnets" "private_subnets" {
  count = local.environment == "qa" ? 1 : 0
  filter {
    name   = "vpc-id"
    values = ["vpc-05a45811de542f6bf"]
  }
  filter {
    name   = "tag:Name"
    values = ["gl-vpc-qa-private-*"]
  }
}
data "aws_subnets" "public_subnets" {
  count = local.environment == "qa" ? 1 : 0
  filter {
    name   = "vpc-id"
    values = ["vpc-05a45811de542f6bf"]
  }
  filter {
    name   = "tag:Name"
    values = ["gl-vpc-qa-public-*"]
  }
}
locals {
  qa_private_subnet_id = try([tolist(data.aws_subnets.private_subnets[0].ids)[0]], [])
  qa_public_subnet_id  = try(data.aws_subnets.public_subnets[0].ids, [])
}

module "api_gateway" {
  source             = "../../../modules/api_alb_ingress"
  count              = 1
  environment        = local.environment
  project            = local.project
  private_subnet_ids = module.vpc[count.index].private_subnets
  vpc_id             = module.vpc[count.index].vpc_id
  domain_name        = local.domain_name
  data_trace         = local.environment == "prod" ? false : true
  certificate_arn    = local.certificate_arn
  # access_logs_bucket_name = module.s3[count.index].nlb_access_logs_bucket_name
  alb_hostname           = "k8s-shared-ca00d1c463"
  api_res_meth_mappings  = file("ledger_api.yaml")
  admin_ui_meth_mappings = file("ledger_admin_ui.yaml")
  cognito_user_pool_arn  = local.cognito_user_pool_arn
  aws_account            = local.aws_account_number
  enable_waf             = true
  public_subnet_ids      = module.vpc[count.index].public_subnets
  util_lb_name           = local.environment == "staging" ? "${local.project}-util-${local.environment}" : "${local.project}-util"
  create_util_nlb        = local.environment == "qa" || local.environment == "staging" ? true : false
  create_util_tg         = local.environment == "prod" || local.environment == "dev" ? false : true
  eks_cluster_name       = local.eks_cluster_name
  tags                   = module.tags[count.index].tags
  depends_on             = [module.K8s_services]
}

module "codepipeline" {
  source = "../../../modules/codepipeline"
  count  = local.environment == "qa" ? 1 : 0

  project                   = local.project
  codepipeline_bucket_name  = "${local.project}-codepipeline-${local.environment}"
  codepipeline_role_name    = "${local.project}-codepipeline-role-${local.environment}"
  kms_key_arn               = module.kms[count.index].cmk_s3pipeline_arn
  codebuild_vpc_id          = module.vpc[count.index].vpc_id
  codebuild_private_subnets = module.vpc[count.index].private_subnets
  codebuild_sg_id           = ["sg-05339575f621dfab3"]
}

# module "elasticache_redis" {
#   source = "../../../modules/elasticache"
#   count  = local.environment == "dev" ? 0 : 1

#   replication_group_id      = "${local.project}-redis-replication-group-${local.environment}"
#   project                   = local.project
#   environment               = local.environment
#   account_id                = local.aws_account_number
#   availability_zones        = var.vpc_azs
#   multi_az_enabled          = local.environment == "qa" ? false : true
#   replicas_per_node_group   = 1
#   engine_type               = "redis"
#   engine_version            = "7.0"
#   node_type                 = local.elasticache_instance_type
#   parameter_group_name      = local.elasticache_parameter_group_name
#   security_group_ids        = [module.networking[count.index].redis_sg]
#   subnet_ids                = local.application_subnet_ids
#   log_group_slow_log_name   = "/${upper(local.project)}/${upper(local.environment)}/redis-slow-log"
#   log_group_engine_log_name = "/${upper(local.project)}/${upper(local.environment)}/redis-engine-log"
#   tags                      = module.tags[count.index].tags
# }

module "elasticache_valkey" {
  source = "../../../modules/elasticache"
  count  = 1

  replication_group_id      = "${local.project}-redis-elasticache-cluster-${local.environment}"
  project                   = local.project
  environment               = local.environment
  account_id                = local.aws_account_number
  availability_zones        = var.vpc_azs
  multi_az_enabled          = local.redis_cluster_multi_az
  replicas_per_node_group   = 1
  engine_type               = "redis"
  engine_version            = "7.0"
  node_type                 = local.elasticache_instance_type
  parameter_group_name      = "default.redis7.cluster.on"
  security_group_ids        = [module.networking[count.index].redis_sg]
  subnet_ids                = local.application_subnet_ids
  log_group_slow_log_name   = "/${upper(local.project)}/${upper(local.environment)}/valkey-slow-log"
  log_group_engine_log_name = "/${upper(local.project)}/${upper(local.environment)}/valkey-engine-log"
  tags                      = module.tags[count.index].tags
}

module "admin_ui" {
  source = "../../../modules/admin_ui"
  count  = 1

  project               = local.project
  environment           = local.environment
  ssl_cert_arn          = local.certificate_arn
  bucketname            = "${local.project}-${local.environment}-${local.aws_account_number}"
  static_website_domain = local.static_website_domain_name
}

module "datadog_integration" {
  count                               = local.datadog_integration ? 1 : 0
  source                              = "../../../modules/datadog_integration"
  account_id                          = local.aws_account_number
  datadog_aws_integration_external_id = local.datadog_aws_integration_external_id
  project                             = local.project
  environment                         = local.environment
}

module "kafka_cluster" {
  source  = "cloudposse/msk-apache-kafka-cluster/aws"
  version = "1.3.0"
  count   = 1

  name                                         = "${local.project}-kafka-cluster-${local.environment}"
  vpc_id                                       = module.vpc[count.index].vpc_id
  subnet_ids                                   = local.application_subnet_ids
  allowed_cidr_blocks                          = local.vpc_application_subnets
  kafka_version                                = "3.6.0"
  broker_per_zone                              = local.environment == "prod" ? 3 : 1
  broker_instance_type                         = local.kafka_instance_type
  cloudwatch_logs_enabled                      = true
  cloudwatch_logs_log_group                    = aws_cloudwatch_log_group.msk_broker_logs[count.index].name
  broker_volume_size                           = local.kafka_storage.volume_size
  storage_autoscaling_max_capacity             = local.kafka_storage.max_storage
  allowed_security_group_ids                   = flatten([module.EKS_cluster[count.index].node_security_group_id, local.environment == "prod" ? [module.networking[count.index].connect_server_sg] : [module.networking[count.index].non_prod_connect_server_sg[count.index]]])
  client_sasl_scram_enabled                    = true
  client_sasl_scram_secret_association_arns    = [module.secrets[count.index].kafka_user_password_secret_arn]
  client_sasl_scram_secret_association_enabled = true

  tags = module.tags[count.index].tags
}

resource "aws_cloudwatch_log_group" "msk_broker_logs" {
  count = local.environment == "dev" ? 1 : 1
  name  = "${local.project}_msk_broker_logs_${local.environment}"

  tags = {
    Project     = local.project
    Environment = local.environment
  }
}

module "aws_backup" {
  source = "../../../modules/aws_backup"

  enable = local.environment == "prod" || local.environment == "staging" ? true : false

  environment = local.environment
  project     = local.project

  aws_backup_role = module.iam[0].aws_backup_role_arn
  backup_schedule = "cron(0 7 * * ? *)"
  delete_after    = local.environment == "prod" ? 30 : 7
  resources_arns  = flatten([module.aurora_postgres[0].aurora_cluster_arn])

  tags = module.tags[0].tags
}

module "ssm_state_manager" {
  source = "../../../modules/ssm_state_manager"
  count  = local.environment == "qa" ? 1 : 0

  rds_automation_role_arn = module.iam[count.index].rds_automation_role_arn
  db_cluster_identifier   = "gl-db-devqa"
}

module "sns_topics" {
  source              = "../../../modules/sns_topics"
  count               = 1
  project             = local.project
  environment         = local.environment
  eks_cluster_name    = local.eks_cluster_name
  account_id          = local.aws_account_number
  kafka_topic         = local.kafka_topic
  broker_id           = local.kafka_broker_id
  balance_alert_rcpts = local.balance_alert_subscription
  sns_topic_arn       = ["arn:aws:sns:ca-central-1:${local.aws_account_number}:${local.project}-balance-alerts-${local.environment}"]
  pending_topic_v2    = local.kafka_lstnr_pending_trns_v2
  tags                = module.tags[count.index].tags
}

module "iam_user" {
  source    = "../../../modules/iam_user_module"
  user_name = "${local.environment}.cloudwatch.api"
}

resource "aws_iam_policy_attachment" "cloudwatch_policy_attachment" {
  name       = "CloudWatchReadOnlyAccessAttachment"
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchFullAccess"
  users      = [module.iam_user.user_name]
}


resource "aws_iam_policy_attachment" "cloudwatch_policy_attachment_2" {
  name       = "CloudWatchReadOnlyAccessAttachment"
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchFullAccessV2"
  users      = [module.iam_user.user_name]
}