provider "aws" {
  region = "ca-central-1"
}

provider "kubernetes" {
  host                   = module.EKS_cluster[0].cluster_endpoint
  cluster_ca_certificate = base64decode(module.EKS_cluster[0].cluster_certificate_authority_data)

  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    # This requires the awscli to be installed locally where Terraform is executed
    args = ["eks", "get-token", "--cluster-name", module.EKS_cluster[0].cluster_id]
  }
}

terraform {
  backend "s3" {
    bucket               = "general-ledger-terraform-state"
    key                  = "terraform.tfstate"
    region               = "ca-central-1"
    encrypt              = true
    workspace_key_prefix = "workspaces"
  }
  required_version = "1.2.3"
  required_providers {
    aws = {
      #     version = "~> 2.70.0"
      version = "~> 4.34.0"
    }
  }
}

# This bucket is the only element that should exist before applying terraform config. Steps:
# 1. Create manually
# 2. Enable versioning
# 3. Enable encryption at rest
# 4. Add the following policy:
#{
#    "Version": "2012-10-17",
#    "Statement": [
#        {
#            "Sid": "Share bucket with non-prod account",
#            "Effect": "Allow",
#            "Principal": {
#                "AWS": "arn:aws:iam::************:root"
#            },
#            "Action": [
#                "s3:GetBucketLocation",
#                "s3:ListBucket"
#            ],
#            "Resource": "arn:aws:s3:::general-ledger-terraform-state"
#        },
#        {
#            "Sid": "Share bucket objects with non-prod account",
#            "Effect": "Allow",
#            "Principal": {
#                "AWS": "arn:aws:iam::************:root"
#            },
#            "Action": [
#                "s3:GetObject",
#                "s3:PutObject"
#            ],
#            "Resource": [
#                "arn:aws:s3:::general-ledger-terraform-state/workspaces/dev/*",
#                "arn:aws:s3:::general-ledger-terraform-state/workspaces/qa/*",
#                "arn:aws:s3:::general-ledger-terraform-state/workspaces/staging/*"
#            ]
#        }
#    ]
#}

