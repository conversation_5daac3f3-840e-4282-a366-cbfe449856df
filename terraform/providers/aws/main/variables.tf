#variable "project" {
#  description = "Project owning the resources here contained"
#}

variable "tgw_id" {
  description = "Id of the transit gateway attached to this VPC"
}

variable "vpc_azs" {
  type        = list(string)
  description = "Availability zones for VPC"
}

variable "vpc_region" {
  description = "AWS region for VPC"
}

variable "policy_arns" {
  type    = list(string)
  default = ["arn:aws:iam::aws:policy/CloudWatchFullAccess", "arn:aws:iam::aws:policy/CloudWatchFullAccessV2"]
}

variable "ptc_it_on_prem_networks" {
  type        = list(any)
  description = "CIDR Ranges for PTC IT On Prem networks"
}
