locals {
  environment                         = var.workspace_to_env[terraform.workspace]
  project                             = "gl"
  aws_account_number                  = local.environment == "prod" ? "************" : "************"
  app_server_ami_name                 = var.env_to_app_server_ami_name[local.environment]
  app_server_instance_type            = var.env_to_app_server_instance_type[local.environment]
  app_server_ip_addresses             = var.env_to_app_server_ip_addresses[local.environment]
  app_server_root_block_device        = var.env_to_app_server_root_block_device[local.environment]
  certificate_arn                     = var.env_to_certificate_arn[local.environment]
  connect_server_ip_addresses         = var.env_to_connect_server_ip_addresses[local.environment]
  domain_name                         = var.env_to_domain_name[local.environment]
  cognito_user_pool_arn               = var.env_to_cognito_user_pool_arn[local.environment]
  elasticache_instance_type           = var.env_to_elasticache_instance_type[local.environment]
  redis_cluster_multi_az              = var.env_to_redis_cluster_multi_az[local.environment]
  ec2_public_key                      = var.env_to_ec2_public_key[local.environment]
  vpc_cidr                            = var.env_to_vpc_cidr[local.environment]
  vpc_public_subnets                  = var.env_to_vpc_public_subnets[local.environment]
  vpc_management_subnets              = var.env_to_vpc_management_subnets[local.environment]
  vpc_application_subnets             = var.env_to_vpc_application_subnets[local.environment]
  vpc_database_subnets                = var.env_to_vpc_database_subnets[local.environment]
  vpc_event_subnets                   = var.env_to_vpc_event_subnets[local.environment]
  vpc_internal_subnets                = var.env_to_vpc_internal_subnets[local.environment]
  vpc_additional_routes               = var.env_to_vpc_additional_routes[local.environment]
  eks_cluster_name                    = local.environment == "qa" ? "${local.project}-eks-main-devqa" : "${local.project}-eks-main-${local.environment}"
  reporting_database_subnets          = var.env_to_reporting_database_subnets[local.environment]
  eks_instance                        = var.env_to_eks_instance[local.environment]
  datadog_integration                 = local.environment == "qa" || local.environment == "prod" ? true : false
  datadog_aws_integration_external_id = var.env_to_datadog_aws_integration_external_id[local.environment]
  static_website_domain_name          = var.env_to_static_website_domain_name[local.environment]
  sso_role_arn                        = var.env_to_sso_role_arn[local.environment]
  aws_auth_configmap_group            = var.env_to_aws_auth_configmap_group[local.environment]
  kafka_instance_type                 = var.env_to_kafka_instance_type[local.environment]
  kafka_storage                       = var.env_to_kafka_storage[local.environment]
  kafka_topic                         = var.env_to_kafka_topic[local.environment]
  kafka_broker_id                     = var.env_to_kafka_broker_id[local.environment]
  kafka_lstnr_pending_trns_v2         = var.env_to_kafka_lstnr_pending_trns_v2[local.environment]
  balance_alert_subscription          = var.env_to_balance_alert_subscription[local.environment]
  db_instance_class                   = var.env_to_db_instance_class[local.environment]
  eks_asg_max_size                    = var.env_eks_asg_max_size[local.environment]
  eks_asg_desired_capacity            = var.env_eks_asg_desired_capacity[local.environment]
  eks_asg_min_size                    = var.env_eks_asg_min_size[local.environment]
}

variable "env_eks_asg_max_size" {
  type = map(string)
  default = {
    dev     = "3"
    qa      = "4"
    staging = "5"
    prod    = "5"
  }
}


variable "env_eks_asg_desired_capacity" {
  type = map(string)
  default = {
    dev     = "2"
    qa      = "3"
    staging = "5"
    prod    = "5"
  }
}

variable "env_eks_asg_min_size" {
  type = map(string)
  default = {
    dev     = "0"
    qa      = "0"
    staging = "5"
    prod    = "5"
  }
}
variable "env_to_db_instance_class" {
  type = map(string)
  default = {
    dev     = "db.r6g.large"
    qa      = "db.r6g.2xlarge"
    staging = "db.r6g.4xlarge"
    prod    = "db.r6g.4xlarge"
  }
}


variable "workspace_to_env" {
  type = map(string)

  default = {
    dev     = "dev"
    qa      = "qa"
    staging = "staging"
    prod    = "prod"
  }
}

# !!!!
variable "env_to_certificate_arn" {
  type = map(string)

  default = {
    #   dev     = "arn:aws:acm:us-east-1:524064709863:certificate/ab23652c-f0d2-4928-8793-ef6b37442788"
    #   qa      = "arn:aws:acm:us-east-1:524064709863:certificate/ab23652c-f0d2-4928-8793-ef6b37442788"
    #   staging = "arn:aws:acm:us-east-1:524064709863:certificate/ab23652c-f0d2-4928-8793-ef6b37442788"
    dev     = "arn:aws:acm:us-east-1:************:certificate/101d1ce4-e359-43b8-9063-55166949ca71"
    qa      = "arn:aws:acm:us-east-1:************:certificate/101d1ce4-e359-43b8-9063-55166949ca71"
    staging = "arn:aws:acm:us-east-1:************:certificate/101d1ce4-e359-43b8-9063-55166949ca71"
    prod    = "arn:aws:acm:us-east-1:************:certificate/d00d0ed8-08d1-4292-b50a-5a6785302405"
  }
}
# !!!!
variable "env_to_domain_name" {
  type = map(string)

  default = {
    dev     = "ledger-dev-api.peoplescloud.io"
    qa      = "ledger-qas-api.peoplescloud.io"
    staging = "ledger-stg-api.peoplescloud.io"
    prod    = "ledger-api.peoplesgroup.com"
  }
}

variable "env_to_app_server_ami_name" {
  type = map(string)

  default = {
    dev     = "RHEL-7.8_HVM_GA-20200225-x86_64-1-Hourly2-GP2"
    qa      = "RHEL-7.8_HVM_GA-20200225-x86_64-1-Hourly2-GP2"
    staging = "RHEL-7.8_HVM_GA-20200225-x86_64-1-Hourly2-GP2"
    prod    = "RHEL-7.8_HVM_GA-20200225-x86_64-1-Hourly2-GP2"
  }
}

variable "env_to_app_server_instance_type" {
  type = map(string)

  default = {
    dev     = "t3.micro"
    qa      = ""
    staging = "t3.medium"
    prod    = "t3.medium"
  }
}

variable "env_to_elasticache_instance_type" {
  type = map(string)
  default = {
    dev     = "cache.t3.small"
    qa      = "cache.t3.small"
    staging = "cache.t3.medium"
    prod    = "cache.m4.large"
  }
}

variable "env_to_redis_cluster_multi_az" {
  type = map(bool)
  default = {
    dev     = false
    qa      = false
    staging = true
    prod    = true
  }
}


# !!!!
variable "env_to_app_server_ip_addresses" {
  type = map(string)

  default = {
    dev     = "***********"
    qa      = "*************"
    staging = "*************"
    prod    = "*************"
  }
}

variable "env_to_app_server_root_block_device" {
  type = map(any)

  default = {
    dev = [
      {
        volume_type = "gp2"
        volume_size = 50
      },
    ]
    qa = [
      {
        volume_type = "gp2"
        volume_size = 50
      },
    ]
    staging = [
      {
        volume_type = "gp2"
        volume_size = 50
      },
    ]
    prod = [
      {
        volume_type = "gp2"
        volume_size = 50
      },
    ]
  }
}

variable "env_to_connect_server_ip_addresses" {
  type = map(string)

  default = {
    dev     = "***********"
    qa      = "*************"
    staging = "*************"
    prod    = "*************"
  }
}

# !!!!
variable "env_to_ec2_public_key" {
  type = map(string)

  default = {
    dev     = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCrLOpeYjfUT8OlZu+YOMP6Qnpi0UcQkyJVQV3syiHcCYdwbNaTZmhDGQRF+R1bY2kBYhmv5FJwmfdI3q8xuz25B0qLgZnFwvoyYJw1dp2Qoqi7nqx+2MbAFz2dt/EuHRBcilmX41fIRSWJnbb7crtvPFAdgMnaffUh7BnrCx1/MDC0bHT1c/HEVGuScnaSqz5/wN1lIk85nGlYAZED/u0XFMbRhXKkJdEPSTfHjPgJmf0YlJIP+eSuSCkklWJAxuKzf77KqtRbCCPAya7esKL/rARCku1vb6V7fKvSoXNdS1aaPpRQ7G8d6zKHYNLP3Bh8TMLsAcTt5eoN+hjQ50jUpdd+xeV/eoPMNSvOVWNy0l567BKmKO0mpXyHk8izVfchoPAZwNFlNyXqpYA3FfMrMhADJcDfl8rmaQVNoiYX4gMS8wF7XcBMwKIwfwOWjApDdnwudCVLpGJPvF7/yx4B0tyGHCv7xC/kHF1vlPuFb6lLOsvfqZJaBt1kxeiN5tE= gl-DEVQA"
    qa      = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCrLOpeYjfUT8OlZu+YOMP6Qnpi0UcQkyJVQV3syiHcCYdwbNaTZmhDGQRF+R1bY2kBYhmv5FJwmfdI3q8xuz25B0qLgZnFwvoyYJw1dp2Qoqi7nqx+2MbAFz2dt/EuHRBcilmX41fIRSWJnbb7crtvPFAdgMnaffUh7BnrCx1/MDC0bHT1c/HEVGuScnaSqz5/wN1lIk85nGlYAZED/u0XFMbRhXKkJdEPSTfHjPgJmf0YlJIP+eSuSCkklWJAxuKzf77KqtRbCCPAya7esKL/rARCku1vb6V7fKvSoXNdS1aaPpRQ7G8d6zKHYNLP3Bh8TMLsAcTt5eoN+hjQ50jUpdd+xeV/eoPMNSvOVWNy0l567BKmKO0mpXyHk8izVfchoPAZwNFlNyXqpYA3FfMrMhADJcDfl8rmaQVNoiYX4gMS8wF7XcBMwKIwfwOWjApDdnwudCVLpGJPvF7/yx4B0tyGHCv7xC/kHF1vlPuFb6lLOsvfqZJaBt1kxeiN5tE= gl-DEVQA"
    staging = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCrLOpeYjfUT8OlZu+YOMP6Qnpi0UcQkyJVQV3syiHcCYdwbNaTZmhDGQRF+R1bY2kBYhmv5FJwmfdI3q8xuz25B0qLgZnFwvoyYJw1dp2Qoqi7nqx+2MbAFz2dt/EuHRBcilmX41fIRSWJnbb7crtvPFAdgMnaffUh7BnrCx1/MDC0bHT1c/HEVGuScnaSqz5/wN1lIk85nGlYAZED/u0XFMbRhXKkJdEPSTfHjPgJmf0YlJIP+eSuSCkklWJAxuKzf77KqtRbCCPAya7esKL/rARCku1vb6V7fKvSoXNdS1aaPpRQ7G8d6zKHYNLP3Bh8TMLsAcTt5eoN+hjQ50jUpdd+xeV/eoPMNSvOVWNy0l567BKmKO0mpXyHk8izVfchoPAZwNFlNyXqpYA3FfMrMhADJcDfl8rmaQVNoiYX4gMS8wF7XcBMwKIwfwOWjApDdnwudCVLpGJPvF7/yx4B0tyGHCv7xC/kHF1vlPuFb6lLOsvfqZJaBt1kxeiN5tE= gl-DEVQA"
    prod    = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC+quwC15QkaUAx1lUHn97WUYvV2SP4rh1Q75S2Q4xo0NPD/aX4QGXyfTXg0t2rCz6fa/cYMknMjgtB+R4biDdgO+C+WyaClJds36Ga7IOqK9AX0+UTDGGT1A/faIPDQUTayMoB9FlK8ajDT5Yf/+LjgtyBBv+GrlncsfwkvCogzkCA3uifeluPqBflehJRvVyA8tMW+B2/54fWhnNNmAKLwNgEVlFc5V5WdRXYPnFFusW8EbnFo0SRvIVkjmBxzMzsHATIW6skwu9hLv0W4gPWi6Wt6ouRTuVONDWSmIaqUZcJkbCTuDU9Gp72HZUp6C/iK7SZFxEEYESuKTz2Lai3 general_ledger_prod"
  }
}

variable "env_to_vpc_cidr" {
  type = map(string)

  default = {
    dev     = "**********/20"
    qa      = "**********/16"
    staging = "**********/16"
    prod    = "**********/16"
  }
}

variable "env_to_vpc_public_subnets" {
  type = map(any)

  default = {
    dev = [
      "**********/24",
      "**********/24",
    ]
    qa = [
      "************/24",
      "************/24",
    ]
    staging = [
      "************/24",
      "************/24",
    ]
    prod = [
      "************/24",
      "************/24",
    ]
  }
}

variable "env_to_vpc_database_subnets" {
  type = map(any)

  default = {
    dev     = ["**********/24", "**********/24"]
    qa      = ["************/24", "************/24"]
    staging = ["************/24", "10.167.226.0/24"]
    prod    = ["10.165.225.0/24", "10.165.226.0/24"]
  }
}

variable "env_to_vpc_management_subnets" {
  type = map(any)

  default = {
    dev = [
      "10.168.3.0/24",
      "10.168.4.0/24",
    ]
    qa = [
      "10.166.250.0/24",
      "10.166.251.0/24",
    ]
    staging = [
      "10.167.250.0/24",
      "10.167.251.0/24",
    ]
    prod = [
      "10.165.250.0/24",
      "10.165.251.0/24",
    ]

  }
}

variable "env_to_vpc_application_subnets" {
  type = map(any)

  default = {
    dev     = ["10.168.5.0/24", "10.168.6.0/24"]
    qa      = ["10.166.205.0/24", "10.166.206.0/24"]
    staging = ["10.167.205.0/24", "10.167.206.0/24"]
    prod    = ["10.165.205.0/24", "10.165.206.0/24"]
  }
}

variable "env_to_vpc_internal_subnets" {
  type = map(any)

  default = {
    dev     = ["10.168.9.0/24", "10.168.10.0/24"]
    qa      = ["10.166.105.0/24", "10.166.106.0/24"]
    staging = ["10.167.105.0/24", "10.167.106.0/24"]
    prod    = ["10.165.105.0/24", "10.165.106.0/24"]
  }
}

variable "env_to_vpc_event_subnets" {
  type = map(any)

  default = {
    dev     = ["10.168.11.0/24", "10.168.12.0/24"]
    qa      = ["10.166.125.0/24", "10.166.126.0/24"]
    staging = ["10.167.125.0/24", "10.167.126.0/24"]
    prod    = ["10.165.125.0/24", "10.165.126.0/24"]
  }
}
# !!!!
variable "env_to_vpc_additional_routes" {
  type = map(any)

  default = {
    dev     = ["0.0.0.0/0"]
    qa      = ["0.0.0.0/0"]
    staging = ["0.0.0.0/0"]
    prod    = ["0.0.0.0/0", "10.8.0.0/16", "10.7.0.0/16", "10.14.0.0/16"]
  }
}
# !!!!
variable "env_to_reporting_database_subnets" {
  type = map(any)

  default = {
    dev     = ["10.122.5.0/24", "10.122.7.0/24"]
    qa      = ["10.125.5.0/24", "10.125.7.0/24"]
    staging = ["10.125.5.0/24", "10.125.7.0/24"]
    prod    = ["10.126.5.0/24", "10.126.7.0/24"]
  }
}

variable "env_to_eks_instance" {
  type = map(any)

  default = {
    dev = {
      instance_type = "m7i-flex.large"
      disk_size     = 20
    }
    qa = {
      instance_type = "m7i-flex.large"
      disk_size     = 20
    }
    staging = {
      instance_type = "m7i-flex.large"
      disk_size     = 20
    }
    prod = {
      instance_type = "m7i-flex.large"
      disk_size     = 50
    }
  }
}

variable "env_to_cognito_user_pool_arn" {
  type = map(any)

  default = {
    dev     = "arn:aws:cognito-idp:us-east-2:529566071206:userpool/us-east-2_oH2B5QIUo"
    qa      = "arn:aws:cognito-idp:us-east-2:529566071206:userpool/us-east-2_oH2B5QIUo"
    staging = "arn:aws:cognito-idp:us-east-2:529566071206:userpool/us-east-2_oH2B5QIUo"
    prod    = "arn:aws:cognito-idp:us-east-2:529566071206:userpool/us-east-2_bi5GbxkLa"
  }
}

variable "env_to_datadog_aws_integration_external_id" {
  type = map(any)

  default = {
    dev     = ""
    qa      = "ebea725fd7d441eb943a1c047f4f3f12"
    staging = ""
    prod    = "f401574c57114f7488e2f795a669b6b3"
  }
}

variable "env_to_static_website_domain_name" {
  type = map(string)
  default = {
    dev     = "ledger-admin-dev.peoplescloud.io"
    qa      = "ledger-admin-qa.peoplescloud.io"
    staging = "ledger-admin-stg.peoplescloud.io"
    prod    = "ledger-admin.peoplesgroup.com"
  }
}

variable "env_to_sso_role_arn" {
  type = map(list(string))

  default = {
    dev = [
      "arn:aws:iam::************:role/AWSReservedSSO_Developer-Non-Prod_037bc659e36c54ac",
      "arn:aws:iam::************:role/tol-lambda-eks-dev"
    ]
    qa = [
      "arn:aws:iam::************:role/AWSReservedSSO_Developer-Non-Prod_037bc659e36c54ac",
      "arn:aws:iam::************:role/AWSReservedSSO_CI-CD-Developers_c4f20a72fbfabe76",
      "arn:aws:iam::************:role/tol-lambda-eks-qa"
    ]
    staging = [
      "arn:aws:iam::************:role/AWSReservedSSO_Developer-Non-Prod_037bc659e36c54ac",
      "arn:aws:iam::************:role/AWSReservedSSO_CI-CD-Developers_c4f20a72fbfabe76"
    ]
    prod = [
      "arn:aws:iam::************:role/AWSReservedSSO_Developers-production_126232b4856d12b5"
    ]
  }
}

variable "env_to_aws_auth_configmap_group" {
  type = map(any)

  default = {
    dev     = "system:masters"
    qa      = "system:masters"
    staging = "system:masters"
    prod    = "Readonly-Group"
  }
}

variable "env_to_kafka_instance_type" {
  type = map(any)

  default = {
    dev     = "kafka.t3.small"
    qa      = "kafka.t3.small"
    staging = "kafka.m5.large"
    prod    = "kafka.m5.large"
  }
}

variable "env_to_kafka_storage" {
  type = map(any)

  default = {
    dev = {
      volume_size = 20
      max_storage = 100
    }

    qa = {
      volume_size = 30
      max_storage = 300
    }

    staging = {
      volume_size = 30
      max_storage = 300
    }

    prod = {
      volume_size = 100
      max_storage = 1000
    }
  }
}

variable "env_to_balance_alert_subscription" {
  type = map(any)
  default = {
    dev     = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    qa      = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    staging = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    prod    = ["https://events.pagerduty.com/x-ere/R02EKP05U4G4UKGE9FGNV1EWR0URHLS7"]
  }
}

variable "env_to_kafka_topic" {
  type = map(string)

  default = {
    dev     = "MANUAL_INTERVENTION_TRANSACTIONS_DEV"
    qa      = "MANUAL_INTERVENTION_TRANSACTIONS_QAS"
    staging = "MANUAL_INTERVENTION_TRANSACTIONS_STAGING"
    prod    = "MANUAL_INTERVENTION_TRANSACTIONS"
  }
}

variable "env_to_kafka_lstnr_pending_trns_v2" {
  type = map(string)

  default = {
    dev     = "PENDING_TRANSACTIONS_V2_DEV"
    qa      = "PENDING_TRANSACTIONS_V2_QAS"
    staging = "PENDING_TRANSACTIONS_V2_STAGING"
    prod    = "PENDING_TRANSACTIONS_V2"
  }
}

variable "env_to_kafka_broker_id" {
  type = map(string)

  default = {
    dev     = ""
    qa      = "2"
    staging = "1"
    prod    = "6"
  }
}