## K8s ALB controller for Ingress -- Brief installation guide
The following steps are taken from [here](https://kubernetes-sigs.github.io/aws-load-balancer-controller/v2.4/deploy/installation/)

1. All necessary IAM roles and policies are created with Terraform in **k8s_alb_ingress_services** module. Apply this module first.
2. Add the EKS chart repo to helm:
```sh
helm repo add eks https://aws.github.io/eks-charts
```
3. Update your local repo to make sure that you have the most recent charts.
```
helm repo update
```
4. Install the TargetGroupBinding CRDs if upgrading the chart via helm upgrade.
```sh
kubectl apply -k "github.com/aws/eks-charts/stable/aws-load-balancer-controller//crds?ref=master"
```
5. Helm install command for clusters with IRSA:
```sh
helm install aws-load-balancer-controller eks/aws-load-balancer-controller \
  -n kube-system \
  --set clusterName=< PUT YOUR CLUSTER NAME HERE > \
  --set serviceAccount.create=false \
  --set serviceAccount.name=aws-load-balancer-controller-< PUT ENVIRONMENT NAME HERE > \
  --set image.repository=************.dkr.ecr.ca-central-1.amazonaws.com/amazon/aws-load-balancer-controller
```
6. Verify that ALB for Ingress is created in the EC2 console.
7. Use ALB's name for integration with API Gateway. REST API doesn't accept ALB for Private Link, so, create an intermediary NLB between API and ALB. This process is already configured in the **api_alb_ingress** module. 
